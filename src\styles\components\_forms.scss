// Form Components with Glass Morphism and Khmer Design

// ===== BASE FORM STYLES =====
.form-group {
  margin-bottom: $spacing-6;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  color: $gray-700;
  margin-bottom: $spacing-2;
  @include khmer-text;
  @include text-shadow-soft;
}

.form-label-required {
  &::after {
    content: ' *';
    color: $danger;
  }
}

// ===== INPUT STYLES =====
.form-control {
  @include input-base;
  
  &.form-control-glass {
    @include glass-morphism;
    border: 2px solid rgba(255, 255, 255, 0.3);
    
    &:focus {
      border-color: rgba($primary, 0.6);
      background: rgba(255, 255, 255, 0.4);
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }
  }
  
  &.form-control-gradient {
    border: 2px solid transparent;
    background: linear-gradient($white, $white) padding-box,
                $gradient-temple border-box;
    
    &:focus {
      background: linear-gradient($white, $white) padding-box,
                  $gradient-khmer-gold border-box;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }
  }
}

// ===== INPUT SIZES =====
.form-control-sm {
  padding: $spacing-2 $spacing-3;
  font-size: $font-size-sm;
  border-radius: $border-radius-md;
}

.form-control-lg {
  padding: $spacing-4 $spacing-5;
  font-size: $font-size-lg;
  border-radius: $border-radius-xl;
}

// ===== INPUT STATES =====
.form-control-success {
  border-color: $success;
  
  &:focus {
    border-color: $success;
    box-shadow: 0 0 0 3px rgba($success, 0.1);
  }
}

.form-control-warning {
  border-color: $warning;
  
  &:focus {
    border-color: $warning;
    box-shadow: 0 0 0 3px rgba($warning, 0.1);
  }
}

.form-control-error {
  border-color: $danger;
  
  &:focus {
    border-color: $danger;
    box-shadow: 0 0 0 3px rgba($danger, 0.1);
  }
}

.form-control-disabled {
  background-color: $gray-100;
  color: $gray-500;
  cursor: not-allowed;
  
  &::placeholder {
    color: $gray-400;
  }
}

// ===== TEXTAREA =====
.form-textarea {
  @extend .form-control;
  min-height: 120px;
  resize: vertical;
  
  &.form-textarea-auto {
    resize: none;
    overflow: hidden;
  }
}

// ===== SELECT =====
.form-select {
  @extend .form-control;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right $spacing-3 center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: $spacing-10;
  cursor: pointer;
  
  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%230ea5e9' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }
}

// ===== CHECKBOX & RADIO =====
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-3;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-check-input {
  width: 20px;
  height: 20px;
  margin-right: $spacing-3;
  border: 2px solid $gray-300;
  border-radius: $border-radius-sm;
  background-color: $white;
  cursor: pointer;
  transition: all $transition-base;
  
  &:checked {
    background-color: $primary;
    border-color: $primary;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
    background-size: 12px;
    background-position: center;
    background-repeat: no-repeat;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary, 0.1);
  }
  
  &[type="radio"] {
    border-radius: 50%;
    
    &:checked {
      background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    }
  }
}

.form-check-label {
  font-size: $font-size-sm;
  color: $gray-700;
  cursor: pointer;
  @include khmer-text;
}

// ===== GLASS MORPHISM CHECKBOX =====
.form-check-glass {
  .form-check-input {
    @include glass-morphism;
    border: 2px solid rgba(255, 255, 255, 0.3);
    
    &:checked {
      background: $gradient-temple-blue;
      border-color: transparent;
    }
  }
}

// ===== INPUT GROUPS =====
.input-group {
  display: flex;
  align-items: stretch;
  width: 100%;
  
  .form-control {
    flex: 1;
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: $border-radius-lg;
      border-bottom-left-radius: $border-radius-lg;
    }
    
    &:last-child {
      border-top-right-radius: $border-radius-lg;
      border-bottom-right-radius: $border-radius-lg;
    }
    
    &:not(:first-child) {
      border-left: none;
    }
  }
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: $spacing-3 $spacing-4;
  background-color: $gray-100;
  border: 2px solid $gray-200;
  color: $gray-600;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  
  &:first-child {
    border-top-left-radius: $border-radius-lg;
    border-bottom-left-radius: $border-radius-lg;
    border-right: none;
  }
  
  &:last-child {
    border-top-right-radius: $border-radius-lg;
    border-bottom-right-radius: $border-radius-lg;
    border-left: none;
  }
}

.input-group-glass {
  .input-group-text {
    @include glass-morphism;
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.2);
  }
}

// ===== FLOATING LABELS =====
.form-floating {
  position: relative;
  
  .form-control {
    padding: $spacing-4 $spacing-4 $spacing-2 $spacing-4;
    
    &:focus,
    &:not(:placeholder-shown) {
      padding-top: $spacing-5;
      padding-bottom: $spacing-1;
    }
    
    &::placeholder {
      color: transparent;
    }
  }
  
  .form-label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: $spacing-4;
    margin: 0;
    pointer-events: none;
    border: 2px solid transparent;
    transform-origin: 0 0;
    transition: all $transition-base;
    
    .form-control:focus ~ &,
    .form-control:not(:placeholder-shown) ~ & {
      transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
      color: $primary;
    }
  }
}

// ===== FORM VALIDATION =====
.form-feedback {
  font-size: $font-size-xs;
  margin-top: $spacing-1;
  @include khmer-text;
}

.form-feedback-success {
  @extend .form-feedback;
  color: $success;
}

.form-feedback-warning {
  @extend .form-feedback;
  color: $warning;
}

.form-feedback-error {
  @extend .form-feedback;
  color: $danger;
}

// ===== FORM LAYOUTS =====
.form-inline {
  display: flex;
  align-items: center;
  gap: $spacing-4;
  
  .form-group {
    margin-bottom: 0;
  }
  
  @include mobile-only {
    flex-direction: column;
    align-items: stretch;
    
    .form-group {
      margin-bottom: $spacing-4;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.form-grid {
  display: grid;
  gap: $spacing-6;
  
  &.form-grid-2 {
    grid-template-columns: repeat(2, 1fr);
    
    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }
  
  &.form-grid-3 {
    grid-template-columns: repeat(3, 1fr);
    
    @include tablet-up {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }
}

// ===== SEARCH INPUT =====
.form-search {
  position: relative;
  
  .form-control {
    padding-left: $spacing-10;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z'/%3e%3c/svg%3e");
    background-position: left $spacing-3 center;
    background-repeat: no-repeat;
    background-size: 16px;
  }
  
  &.form-search-glass {
    .form-control {
      @include glass-morphism;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }
  }
}

// ===== ANIMATED FORMS =====
.form-animated {
  .form-group {
    @include stagger-fade-in;
    
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }
  }
}

// ===== RESPONSIVE FORMS =====
@include mobile-only {
  .form-control {
    font-size: 16px; // Prevents zoom on iOS
  }
  
  .form-grid {
    gap: $spacing-4;
  }
  
  .input-group {
    flex-direction: column;
    
    .form-control,
    .input-group-text {
      border-radius: $border-radius-lg;
      border: 2px solid $gray-200;
      
      &:not(:last-child) {
        margin-bottom: $spacing-2;
      }
    }
  }
}
