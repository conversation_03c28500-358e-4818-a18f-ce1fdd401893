// Navigation Components with Glass Morphism

// ===== BASE NAVIGATION =====
.nav {
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  list-style: none;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: $spacing-3 $spacing-4;
  color: $gray-700;
  text-decoration: none;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-lg;
  transition: all $transition-base;
  @include khmer-text;
  
  &:hover {
    color: $primary;
    background: alpha($primary, 0.1);
    transform: translateY(-1px);
  }
  
  &.nav-link-active {
    color: $primary;
    background: alpha($primary, 0.15);
    font-weight: $font-weight-semibold;
  }
  
  .nav-icon {
    margin-right: $spacing-2;
    font-size: $font-size-lg;
  }
}

// ===== GLASS NAVIGATION =====
.nav-glass {
  @include glass-morphism;
  border-radius: $border-radius-xl;
  padding: $spacing-2;
  
  .nav-link {
    color: $gray-800;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      color: $primary;
    }
    
    &.nav-link-active {
      background: rgba(255, 255, 255, 0.4);
      color: $primary;
    }
  }
}

// ===== BOTTOM NAVIGATION =====
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: $z-index-fixed;
  @include glass-morphism;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: $spacing-2 $spacing-4;
  
  .nav {
    justify-content: space-around;
    width: 100%;
  }
  
  .nav-link {
    @include flex-column-center;
    padding: $spacing-2;
    min-width: 60px;
    color: $gray-600;
    font-size: $font-size-xs;
    
    &:hover {
      color: $primary;
      background: transparent;
      transform: translateY(-2px);
    }
    
    &.nav-link-active {
      color: $primary;
      background: transparent;
      
      .nav-icon {
        @include floating-animation;
      }
    }
    
    .nav-icon {
      margin-right: 0;
      margin-bottom: $spacing-1;
      font-size: $font-size-xl;
    }
  }
}

// ===== SIDEBAR NAVIGATION =====
.sidebar-nav {
  width: 280px;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  @include glass-morphism;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  padding: $spacing-6;
  overflow-y: auto;
  z-index: $z-index-fixed;
  
  .nav {
    flex-direction: column;
    align-items: stretch;
  }
  
  .nav-item {
    margin-bottom: $spacing-2;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .nav-link {
    width: 100%;
    justify-content: flex-start;
    padding: $spacing-3 $spacing-4;
    
    &:hover {
      transform: translateX(4px);
      background: rgba(255, 255, 255, 0.2);
    }
    
    &.nav-link-active {
      background: $gradient-temple-blue;
      color: $white;
      @include text-shadow-soft;
      
      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 60%;
        background: $white;
        border-radius: $border-radius-sm;
      }
    }
  }
  
  @include mobile-only {
    width: 100%;
    transform: translateX(-100%);
    transition: transform $transition-base;
    
    &.sidebar-nav-open {
      transform: translateX(0);
    }
  }
}

// ===== BREADCRUMB NAVIGATION =====
.breadcrumb {
  display: flex;
  align-items: center;
  padding: $spacing-3 0;
  margin: 0;
  list-style: none;
  @include glass-morphism;
  border-radius: $border-radius-lg;
  padding: $spacing-3 $spacing-4;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  
  &:not(:last-child)::after {
    content: '/';
    margin: 0 $spacing-2;
    color: $gray-400;
  }
  
  a {
    color: $gray-600;
    text-decoration: none;
    font-weight: $font-weight-medium;
    @include khmer-text;
    
    &:hover {
      color: $primary;
    }
  }
  
  &.breadcrumb-item-active {
    color: $gray-800;
    font-weight: $font-weight-semibold;
  }
}

// ===== TAB NAVIGATION =====
.nav-tabs {
  border-bottom: 2px solid $gray-200;
  margin-bottom: $spacing-6;
  
  .nav-link {
    border-radius: $border-radius-lg $border-radius-lg 0 0;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    
    &:hover {
      border-bottom-color: $gray-300;
      background: $gray-50;
    }
    
    &.nav-link-active {
      border-bottom-color: $primary;
      background: $white;
      color: $primary;
    }
  }
}

.nav-tabs-glass {
  @extend .nav-tabs;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  
  .nav-link {
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-bottom-color: rgba(255, 255, 255, 0.4);
    }
    
    &.nav-link-active {
      background: rgba(255, 255, 255, 0.3);
      border-bottom-color: $primary;
    }
  }
}

// ===== PILL NAVIGATION =====
.nav-pills {
  .nav-link {
    border-radius: $border-radius-full;
    
    &.nav-link-active {
      @include gradient-temple-blue;
      color: $white;
      @include text-shadow-soft;
    }
  }
}

// ===== NAVBAR =====
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-4 $spacing-6;
  @include glass-morphism;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  
  .navbar-brand {
    display: flex;
    align-items: center;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
    text-decoration: none;
    @include khmer-text;
    @include text-shadow-soft;
    
    .navbar-logo {
      width: 32px;
      height: 32px;
      margin-right: $spacing-3;
    }
  }
  
  .navbar-nav {
    display: flex;
    align-items: center;
    gap: $spacing-2;
    
    @include mobile-only {
      display: none;
    }
  }
  
  .navbar-toggle {
    display: none;
    @include button-base;
    @include glass-morphism;
    padding: $spacing-2;
    
    @include mobile-only {
      display: flex;
    }
  }
}

// ===== DROPDOWN NAVIGATION =====
.nav-dropdown {
  position: relative;
  
  .nav-dropdown-toggle {
    &::after {
      content: '▼';
      margin-left: $spacing-2;
      font-size: $font-size-xs;
      transition: transform $transition-base;
    }
    
    &.nav-dropdown-open::after {
      transform: rotate(180deg);
    }
  }
  
  .nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    @include glass-morphism;
    border-radius: $border-radius-lg;
    padding: $spacing-2;
    box-shadow: $shadow-lg;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all $transition-base;
    z-index: $z-index-dropdown;
    
    &.nav-dropdown-open {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
    
    .nav-dropdown-item {
      display: block;
      padding: $spacing-2 $spacing-3;
      color: $gray-700;
      text-decoration: none;
      border-radius: $border-radius-md;
      transition: all $transition-base;
      @include khmer-text;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        color: $primary;
        transform: translateX(4px);
      }
    }
  }
}

// ===== ANIMATED NAVIGATION =====
.nav-animated {
  .nav-item {
    @include stagger-fade-in;
    
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }
  }
}

// ===== ENHANCED MOBILE NAVIGATION =====
.mobile-menu-overlay {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  z-index: 200;
  animation: fadeIn 0.3s ease-out;

  .mobile-menu-content {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
    border-radius: 1rem 1rem 0 0;
    padding: 1.5rem;
    margin-top: 2rem;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideUpIn 0.3s ease-out;

    .mobile-user-info {
      text-align: center;
      padding-bottom: 1.5rem;
      border-bottom: 1px solid var(--border-primary);
      margin-bottom: 1.5rem;
    }

    .mobile-nav-links {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .mobile-nav-link {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 0.75rem;
        color: var(--text-secondary);
        font-weight: 500;
        transition: all 0.2s ease-in-out;
        cursor: pointer;

        &:hover {
          background: var(--color-primary-light);
          color: var(--color-primary);
          transform: translateX(4px);
        }

        &.mobile-nav-link-active {
          background: var(--color-primary-light);
          color: var(--color-primary);
          font-weight: 600;
          border-left: 4px solid var(--color-primary);
        }
      }
    }
  }
}

// ===== ENHANCED BOTTOM NAVIGATION =====
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: $z-index-fixed;
  @include glass-morphism;
  border-top: 1px solid var(--border-primary);
  padding: $spacing-2 $spacing-1;
  background: var(--navbar-bg);
  backdrop-filter: var(--glass-backdrop);

  .bottom-nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 100%;
  }

  .bottom-nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: $spacing-2;
    min-width: 60px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
    }

    &.bottom-nav-item-active {
      .bottom-nav-icon {
        color: var(--color-primary);
        transform: scale(1.1);
      }

      .bottom-nav-label {
        color: var(--color-primary);
        font-weight: 600;
      }
    }
  }

  .bottom-nav-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: $spacing-1;
    transition: all 0.2s ease-in-out;

    &.bottom-nav-icon-active {
      color: var(--color-primary);
      animation: bounce 0.6s ease-in-out;
    }
  }

  .bottom-nav-label {
    font-size: $font-size-xs;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.2;
    transition: all 0.2s ease-in-out;

    &.bottom-nav-label-active {
      color: var(--color-primary);
      font-weight: 600;
    }
  }

  .bottom-nav-indicator {
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: var(--color-primary);
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
}

// ===== MAIN CONTENT LAYOUT =====
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 80px);
  padding-bottom: 80px;

  @media (min-width: 769px) {
    margin-left: 16rem;
    min-height: 100vh;
    padding-bottom: 0;
  }

  .content-container {
    flex: 1;
    padding: 1rem;

    @media (min-width: 769px) {
      padding: 2rem;
    }
  }
}

// ===== RESPONSIVE NAVIGATION =====
@include mobile-only {
  .navbar {
    padding: $spacing-3 $spacing-4;
  }

  .nav-link {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-sm;
  }

  .sidebar-nav {
    .nav-link {
      padding: $spacing-4;
      font-size: $font-size-base;
    }
  }

  .sidebar {
    display: none;
  }

  .bottom-nav {
    display: flex;
  }

  .hidden {
    display: none !important;
  }

  .md\\:hidden {
    display: block !important;
  }
}

@media (min-width: 769px) {
  .bottom-nav {
    display: none;
  }

  .sidebar {
    display: flex;
  }

  .hidden {
    display: block !important;
  }

  .md\\:hidden {
    display: none !important;
  }

  .md\\:flex {
    display: flex !important;
  }
}
