import { useState, useEffect } from '@lynx-js/react'
import { Employee, Loan } from '../../types'
import { formatCurrency } from '../../utils/currency'
import { formatDate } from '../../utils/date'

interface LoanManagementProps {
  currentUser: Employee
  language: 'en' | 'km'
}

// Mock loan data
const MOCK_LOANS: Loan[] = [
  {
    id: '1',
    clientId: '1',
    loanType: 'personal',
    principalAmount: 5000,
    interestRate: 12,
    termMonths: 12,
    monthlyPayment: 444.24,
    totalAmount: 5330.89,
    purpose: 'Business expansion',
    purposeKhmer: 'ពង្រីកអាជីវកម្ម',
    status: 'active',
    disbursementDate: new Date('2024-01-15'),
    maturityDate: new Date('2025-01-15'),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-06-20'),
    createdBy: 'admin',
    approvedBy: 'manager',
    approvedAt: new Date('2024-01-12')
  },
  {
    id: '2',
    clientId: '2',
    loanType: 'business',
    principalAmount: 10000,
    interestRate: 15,
    termMonths: 24,
    monthlyPayment: 484.97,
    totalAmount: 11639.28,
    purpose: 'Equipment purchase',
    status: 'pending',
    createdAt: new Date('2024-06-15'),
    updatedAt: new Date('2024-06-20'),
    createdBy: 'officer'
  }
]

export function LoanManagement({ currentUser, language }: LoanManagementProps) {
  const [loans, setLoans] = useState<Loan[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setLoans(MOCK_LOANS)
      setIsLoading(false)
    }, 1000)
  }, [])

  const texts = {
    en: {
      title: 'Loan Management',
      all: 'All',
      pending: 'Pending',
      active: 'Active',
      completed: 'Completed',
      defaulted: 'Defaulted',
      amount: 'Amount',
      term: 'Term',
      status: 'Status',
      client: 'Client',
      type: 'Type',
      created: 'Created',
      loading: 'Loading loans...',
      noLoans: 'No loans found',
      months: 'months',
      personal: 'Personal',
      business: 'Business',
      pawnshop: 'Pawnshop',
      emergency: 'Emergency'
    },
    km: {
      title: 'គ្រប់គ្រងកម្ចី',
      all: 'ទាំងអស់',
      pending: 'កំពុងរង់ចាំ',
      active: 'សកម្ម',
      completed: 'បានបញ្ចប់',
      defaulted: 'ខកខាន',
      amount: 'ចំនួនទឹកប្រាក់',
      term: 'រយៈពេល',
      status: 'ស្ថានភាព',
      client: 'អតិថិជន',
      type: 'ប្រភេទ',
      created: 'បានបង្កើត',
      loading: 'កំពុងផ្ទុកកម្ចី...',
      noLoans: 'រកមិនឃើញកម្ចី',
      months: 'ខែ',
      personal: 'ផ្ទាល់ខ្លួន',
      business: 'អាជីវកម្ម',
      pawnshop: 'បញ្ចាំ',
      emergency: 'បន្ទាន់'
    }
  }

  const t = texts[language]

  const filteredLoans = selectedStatus === 'all' 
    ? loans 
    : loans.filter(loan => loan.status === selectedStatus)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#ffc107'
      case 'active': return '#28a745'
      case 'completed': return '#007bff'
      case 'defaulted': return '#dc3545'
      default: return '#6c757d'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return t.pending
      case 'active': return t.active
      case 'completed': return t.completed
      case 'defaulted': return t.defaulted
      default: return status
    }
  }

  const getLoanTypeText = (type: string) => {
    switch (type) {
      case 'personal': return t.personal
      case 'business': return t.business
      case 'pawnshop': return t.pawnshop
      case 'emergency': return t.emergency
      default: return type
    }
  }

  if (isLoading) {
    return (
      <view style={{ 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center' 
      }}>
        <text style={{ fontSize: 18, color: '#666' }}>
          {t.loading}
        </text>
      </view>
    )
  }

  return (
    <view style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <view style={{ 
        backgroundColor: 'white',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0'
      }}>
        <text style={{ 
          fontSize: 24,
          fontWeight: 'bold',
          color: '#333',
          marginBottom: 15
        }}>
          {t.title}
        </text>

        {/* Status Filter */}
        <scroll-view horizontal style={{ marginBottom: 10 }}>
          <view style={{ flexDirection: 'row' }}>
            {['all', 'pending', 'active', 'completed', 'defaulted'].map((status) => (
              <view 
                key={status}
                style={{
                  backgroundColor: selectedStatus === status ? '#007bff' : '#f8f9fa',
                  borderRadius: 20,
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  marginRight: 10
                }}
                bindtap={() => setSelectedStatus(status)}
              >
                <text style={{
                  color: selectedStatus === status ? 'white' : '#666',
                  fontSize: 14,
                  fontWeight: selectedStatus === status ? '600' : 'normal'
                }}>
                  {status === 'all' ? t.all : getStatusText(status)}
                </text>
              </view>
            ))}
          </view>
        </scroll-view>

        {/* Stats */}
        <view style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 20, fontWeight: 'bold', color: '#007bff' }}>
              {loans.length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.all}
            </text>
          </view>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 20, fontWeight: 'bold', color: '#28a745' }}>
              {loans.filter(l => l.status === 'active').length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.active}
            </text>
          </view>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 20, fontWeight: 'bold', color: '#ffc107' }}>
              {loans.filter(l => l.status === 'pending').length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.pending}
            </text>
          </view>
        </view>
      </view>

      {/* Loan List */}
      <scroll-view style={{ flex: 1 }}>
        <view style={{ padding: 20 }}>
          {filteredLoans.length === 0 ? (
            <view style={{ 
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 40
            }}>
              <text style={{ fontSize: 16, color: '#666' }}>
                {t.noLoans}
              </text>
            </view>
          ) : (
            filteredLoans.map((loan) => (
              <LoanCard
                key={loan.id}
                loan={loan}
                language={language}
                getStatusColor={getStatusColor}
                getStatusText={getStatusText}
                getLoanTypeText={getLoanTypeText}
              />
            ))
          )}
        </view>
      </scroll-view>

      {/* Add Button */}
      <view 
        style={{
          position: 'absolute',
          bottom: 20,
          right: 20,
          width: 56,
          height: 56,
          backgroundColor: '#007bff',
          borderRadius: 28,
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <text style={{ color: 'white', fontSize: 24 }}>+</text>
      </view>
    </view>
  )
}

function LoanCard({ loan, language, getStatusColor, getStatusText, getLoanTypeText }: {
  loan: Loan
  language: 'en' | 'km'
  getStatusColor: (status: string) => string
  getStatusText: (status: string) => string
  getLoanTypeText: (type: string) => string
}) {
  return (
    <view style={{
      backgroundColor: 'white',
      borderRadius: 12,
      padding: 15,
      marginBottom: 10,
      borderLeftWidth: 4,
      borderLeftColor: getStatusColor(loan.status)
    }}>
      <view style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 10 }}>
        <view style={{ flex: 1 }}>
          <text style={{ 
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333',
            marginBottom: 5
          }}>
            {formatCurrency(loan.principalAmount)}
          </text>
          <text style={{ fontSize: 14, color: '#666', marginBottom: 3 }}>
            {getLoanTypeText(loan.loanType)} • {loan.termMonths} {language === 'km' ? 'ខែ' : 'months'}
          </text>
          <text style={{ fontSize: 14, color: '#666' }}>
            {language === 'km' ? 'បានបង្កើត:' : 'Created:'} {formatDate(loan.createdAt, language)}
          </text>
        </view>
        
        <view style={{
          backgroundColor: getStatusColor(loan.status),
          borderRadius: 12,
          paddingHorizontal: 8,
          paddingVertical: 4
        }}>
          <text style={{ 
            color: 'white',
            fontSize: 12,
            fontWeight: '600'
          }}>
            {getStatusText(loan.status)}
          </text>
        </view>
      </view>

      {loan.purpose && (
        <text style={{ fontSize: 14, color: '#666', fontStyle: 'italic' }}>
          {language === 'km' ? loan.purposeKhmer || loan.purpose : loan.purpose}
        </text>
      )}
    </view>
  )
}
