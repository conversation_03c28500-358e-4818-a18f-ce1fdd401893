// SCSS Functions for Khmer Loan Management App

// ===== COLOR FUNCTIONS =====

// Lighten a color by mixing it with white
@function lighten-color($color, $percentage) {
  @return mix(white, $color, $percentage);
}

// Darken a color by mixing it with black
@function darken-color($color, $percentage) {
  @return mix(black, $color, $percentage);
}

// Create a transparent version of a color
@function alpha($color, $opacity) {
  @return rgba($color, $opacity);
}

// ===== SPACING FUNCTIONS =====

// Convert pixel values to rem
@function px-to-rem($px, $base-font-size: 16px) {
  @return ($px / $base-font-size) * 1rem;
}

// Convert rem values to pixels
@function rem-to-px($rem, $base-font-size: 16px) {
  @return ($rem * $base-font-size);
}

// Get spacing value by multiplier
@function spacing($multiplier) {
  @return $spacing-4 * $multiplier;
}

// ===== TYPOGRAPHY FUNCTIONS =====

// Calculate line height based on font size
@function line-height($font-size, $target-height) {
  @return $target-height / $font-size;
}

// Fluid typography scaling
@function fluid-type($min-size, $max-size, $min-width: 320px, $max-width: 1200px) {
  @return calc(#{$min-size} + #{strip-unit($max-size - $min-size)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
}

// ===== UTILITY FUNCTIONS =====

// Remove unit from a number
@function strip-unit($number) {
  @if type-of($number) == 'number' and not unitless($number) {
    @return $number / ($number * 0 + 1);
  }
  @return $number;
}

// Check if a value is a valid length
@function is-length($value) {
  @return type-of($value) == 'number' and not unitless($value);
}

// Get a value from a map with fallback
@function map-get-fallback($map, $key, $fallback: null) {
  @if map-has-key($map, $key) {
    @return map-get($map, $key);
  }
  @return $fallback;
}

// ===== GRADIENT FUNCTIONS =====

// Create a linear gradient with angle
@function linear-gradient-angle($angle, $color-stops...) {
  @return linear-gradient($angle, $color-stops);
}

// Create a radial gradient
@function radial-gradient-circle($color-stops...) {
  @return radial-gradient(circle, $color-stops);
}

// ===== SHADOW FUNCTIONS =====

// Create a box shadow with color
@function colored-shadow($x, $y, $blur, $spread, $color, $opacity: 0.15) {
  @return #{$x} #{$y} #{$blur} #{$spread} rgba($color, $opacity);
}

// Create multiple shadows
@function multi-shadow($shadows...) {
  @return $shadows;
}

// ===== BORDER RADIUS FUNCTIONS =====

// Create asymmetric border radius
@function border-radius-asymmetric($top-left, $top-right, $bottom-right, $bottom-left) {
  @return #{$top-left} #{$top-right} #{$bottom-right} #{$bottom-left};
}

// ===== ANIMATION FUNCTIONS =====

// Create cubic bezier timing function
@function ease($type: 'ease-out') {
  $easings: (
    'ease-in': cubic-bezier(0.4, 0, 1, 1),
    'ease-out': cubic-bezier(0, 0, 0.2, 1),
    'ease-in-out': cubic-bezier(0.4, 0, 0.2, 1),
    'ease-in-back': cubic-bezier(0.6, -0.28, 0.735, 0.045),
    'ease-out-back': cubic-bezier(0.175, 0.885, 0.32, 1.275),
    'ease-in-out-back': cubic-bezier(0.68, -0.55, 0.265, 1.55)
  );
  
  @return map-get($easings, $type);
}

// ===== RESPONSIVE FUNCTIONS =====

// Calculate responsive value
@function responsive-value($min-value, $max-value, $min-width: 320px, $max-width: 1200px) {
  @return calc(#{$min-value} + #{strip-unit($max-value - $min-value)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
}

// ===== KHMER SPECIFIC FUNCTIONS =====

// Get Khmer gold color by shade
@function khmer-gold($shade: 500) {
  $colors: (
    50: $khmer-gold-50,
    100: $khmer-gold-100,
    200: $khmer-gold-200,
    300: $khmer-gold-300,
    400: $khmer-gold-400,
    500: $khmer-gold-500,
    600: $khmer-gold-600,
    700: $khmer-gold-700,
    800: $khmer-gold-800,
    900: $khmer-gold-900
  );
  
  @return map-get($colors, $shade);
}

// Get temple blue color by shade
@function temple-blue($shade: 500) {
  $colors: (
    50: $temple-blue-50,
    100: $temple-blue-100,
    200: $temple-blue-200,
    300: $temple-blue-300,
    400: $temple-blue-400,
    500: $temple-blue-500,
    600: $temple-blue-600,
    700: $temple-blue-700,
    800: $temple-blue-800,
    900: $temple-blue-900
  );
  
  @return map-get($colors, $shade);
}

// Get lotus pink color by shade
@function lotus-pink($shade: 500) {
  $colors: (
    50: $lotus-pink-50,
    100: $lotus-pink-100,
    200: $lotus-pink-200,
    300: $lotus-pink-300,
    400: $lotus-pink-400,
    500: $lotus-pink-500,
    600: $lotus-pink-600,
    700: $lotus-pink-700,
    800: $lotus-pink-800,
    900: $lotus-pink-900
  );
  
  @return map-get($colors, $shade);
}
