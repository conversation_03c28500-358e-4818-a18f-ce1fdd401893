import { create } from 'zustand'
import { persist, subscribeWithSelector } from 'zustand/middleware'

// ===== TYPES =====
export type ThemeMode = 'light' | 'dark' | 'system'
export type ResolvedTheme = 'light' | 'dark'

interface ThemeState {
  // State
  mode: ThemeMode
  resolvedTheme: ResolvedTheme
  systemTheme: ResolvedTheme
  isLoading: boolean
  enableTransitions: boolean
  
  // Actions
  setTheme: (mode: ThemeMode) => void
  toggleTheme: () => void
  setSystemTheme: (theme: ResolvedTheme) => void
  setEnableTransitions: (enabled: boolean) => void
  setLoading: (loading: boolean) => void
  
  // Computed
  isDark: () => boolean
  isLight: () => boolean
}

// ===== UTILITIES =====
const getSystemTheme = (): ResolvedTheme => {
  if (typeof window === 'undefined') return 'light'
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

const resolveTheme = (mode: ThemeMode, systemTheme: ResolvedTheme): ResolvedTheme => {
  return mode === 'system' ? systemTheme : mode
}

const applyTheme = (theme: ResolvedTheme, enableTransitions: boolean): void => {
  if (typeof document === 'undefined') return
  
  const root = document.documentElement
  const body = document.body
  
  // Disable transitions during theme change if needed
  if (!enableTransitions) {
    body.classList.add('theme-switching')
  }
  
  // Remove old theme classes
  body.classList.remove('theme-light', 'theme-dark')
  
  // Apply new theme
  root.setAttribute('data-theme', theme)
  body.classList.add(`theme-${theme}`)
  
  // Re-enable transitions after a brief delay
  if (!enableTransitions) {
    setTimeout(() => {
      body.classList.remove('theme-switching')
    }, 50)
  }
}

// ===== THEME STORE =====
export const useThemeStore = create<ThemeState>()(
  persist(
    subscribeWithSelector((set, get) => ({
      // Initial state
      mode: 'system',
      resolvedTheme: 'light',
      systemTheme: getSystemTheme(),
      isLoading: true,
      enableTransitions: true,

      // Actions
      setTheme: (mode: ThemeMode) => {
        const { systemTheme } = get()
        const resolvedTheme = resolveTheme(mode, systemTheme)
        set({ mode, resolvedTheme })
      },

      toggleTheme: () => {
        const { mode, systemTheme } = get()
        if (mode === 'system') {
          // If system, switch to opposite of current system theme
          const newMode = systemTheme === 'dark' ? 'light' : 'dark'
          get().setTheme(newMode)
        } else {
          // Toggle between light and dark
          const newMode = mode === 'light' ? 'dark' : 'light'
          get().setTheme(newMode)
        }
      },

      setSystemTheme: (systemTheme: ResolvedTheme) => {
        const { mode } = get()
        const resolvedTheme = resolveTheme(mode, systemTheme)
        set({ systemTheme, resolvedTheme })
      },

      setEnableTransitions: (enableTransitions: boolean) => {
        set({ enableTransitions })
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading })
      },

      // Computed
      isDark: () => get().resolvedTheme === 'dark',
      isLight: () => get().resolvedTheme === 'light'
    })),
    {
      name: 'khmer-loan-theme',
      partialize: (state) => ({
        mode: state.mode,
        enableTransitions: state.enableTransitions
      })
    }
  )
)

// ===== THEME EFFECTS =====
// Subscribe to theme changes and apply them
useThemeStore.subscribe(
  (state) => state.resolvedTheme,
  (resolvedTheme) => {
    const { enableTransitions, isLoading } = useThemeStore.getState()
    if (!isLoading) {
      applyTheme(resolvedTheme, enableTransitions)
    }
  }
)

// Listen for system theme changes
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  
  const handleChange = (e: MediaQueryListEvent) => {
    useThemeStore.getState().setSystemTheme(e.matches ? 'dark' : 'light')
  }

  mediaQuery.addEventListener('change', handleChange)
}

// ===== SELECTORS =====
export const useTheme = () => useThemeStore((state) => ({
  mode: state.mode,
  resolvedTheme: state.resolvedTheme,
  systemTheme: state.systemTheme,
  isLoading: state.isLoading,
  enableTransitions: state.enableTransitions,
  setTheme: state.setTheme,
  toggleTheme: state.toggleTheme,
  setEnableTransitions: state.setEnableTransitions,
  isDark: state.isDark(),
  isLight: state.isLight()
}))

export const useResolvedTheme = () => useThemeStore((state) => state.resolvedTheme)
export const useThemeMode = () => useThemeStore((state) => state.mode)
export const useIsDarkTheme = () => useThemeStore((state) => state.isDark())

// ===== INITIALIZATION =====
// Initialize theme on app start
export const initializeTheme = () => {
  const store = useThemeStore.getState()
  store.setSystemTheme(getSystemTheme())
  store.setLoading(false)
}
