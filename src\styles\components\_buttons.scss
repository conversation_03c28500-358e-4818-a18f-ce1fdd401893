// Button Components with Khmer-Inspired Design

// ===== BASE BUTTON =====
.btn {
  @include button-base;
  
  // Icon support
  .btn-icon {
    margin-right: $spacing-2;
    
    &.btn-icon-right {
      margin-right: 0;
      margin-left: $spacing-2;
    }
    
    &.btn-icon-only {
      margin: 0;
    }
  }
}

// ===== GRADIENT BUTTONS =====
.btn-primary {
  @include button-gradient($gradient-temple-blue, $shadow-temple-blue);
}

.btn-secondary {
  @include button-gradient($gradient-khmer-gold, $shadow-khmer-gold);
}

.btn-accent {
  @include button-gradient($gradient-lotus-pink, $shadow-lotus-pink);
}

.btn-success {
  @include button-gradient(
    linear-gradient(135deg, $success 0%, darken($success, 10%) 100%),
    0 8px 32px rgba($success, 0.15)
  );
}

.btn-warning {
  @include button-gradient($gradient-khmer-gold, $shadow-khmer-gold);
}

.btn-danger {
  @include button-gradient(
    linear-gradient(135deg, $danger 0%, darken($danger, 10%) 100%),
    0 8px 32px rgba($danger, 0.15)
  );
}

.btn-info {
  @include button-gradient($gradient-temple-blue, $shadow-temple-blue);
}

// ===== SPECIAL GRADIENT BUTTONS =====
.btn-sunset {
  @include button-gradient($gradient-sunset, 0 8px 32px rgba($khmer-gold-500, 0.2));
}

.btn-temple {
  @include button-gradient($gradient-temple, 0 8px 32px rgba($temple-blue-500, 0.2));
}

// ===== OUTLINE BUTTONS =====
.btn-outline {
  @include button-base;
  background: transparent;
  border: 2px solid $gray-300;
  color: $gray-700;
  
  &:hover:not(:disabled) {
    background: $gray-50;
    border-color: $gray-400;
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }
}

.btn-outline-primary {
  @extend .btn-outline;
  border-color: $primary;
  color: $primary;
  
  &:hover:not(:disabled) {
    background: $primary;
    color: $white;
    box-shadow: $shadow-temple-blue;
  }
}

.btn-outline-secondary {
  @extend .btn-outline;
  border-color: $secondary;
  color: $secondary;
  
  &:hover:not(:disabled) {
    background: $secondary;
    color: $white;
    box-shadow: $shadow-khmer-gold;
  }
}

.btn-outline-accent {
  @extend .btn-outline;
  border-color: $accent;
  color: $accent;
  
  &:hover:not(:disabled) {
    background: $accent;
    color: $white;
    box-shadow: $shadow-lotus-pink;
  }
}

// ===== GLASS BUTTONS =====
.btn-glass {
  @include button-base;
  @include glass-morphism;
  color: $gray-800;
  
  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

.btn-glass-dark {
  @include button-base;
  @include glass-morphism-dark;
  color: $white;
  
  &:hover:not(:disabled) {
    background: rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

// ===== BUTTON SIZES =====
.btn-xs {
  padding: $spacing-1 $spacing-3;
  font-size: $font-size-xs;
  border-radius: $border-radius-md;
}

.btn-sm {
  padding: $spacing-2 $spacing-4;
  font-size: $font-size-sm;
  border-radius: $border-radius-md;
}

.btn-md {
  padding: $spacing-3 $spacing-6;
  font-size: $font-size-base;
  border-radius: $border-radius-lg;
}

.btn-lg {
  padding: $spacing-4 $spacing-8;
  font-size: $font-size-lg;
  border-radius: $border-radius-lg;
}

.btn-xl {
  padding: $spacing-5 $spacing-10;
  font-size: $font-size-xl;
  border-radius: $border-radius-xl;
}

// ===== BUTTON SHAPES =====
.btn-rounded {
  border-radius: $border-radius-full;
}

.btn-square {
  border-radius: 0;
}

.btn-circle {
  border-radius: 50%;
  width: 48px;
  height: 48px;
  padding: 0;
  
  &.btn-sm {
    width: 32px;
    height: 32px;
  }
  
  &.btn-lg {
    width: 64px;
    height: 64px;
  }
}

// ===== BUTTON STATES =====
.btn-loading {
  position: relative;
  color: transparent !important;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: rotate 1s linear infinite;
  }
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  
  &:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

// ===== FLOATING ACTION BUTTON =====
.btn-fab {
  @include button-base;
  @include gradient-temple-blue;
  position: fixed;
  bottom: $spacing-6;
  right: $spacing-6;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  color: $white;
  font-size: $font-size-xl;
  z-index: $z-index-fixed;
  @include floating-animation;
  
  &:hover:not(:disabled) {
    transform: translateY(-4px) scale(1.1);
    box-shadow: $shadow-temple-blue;
    animation-play-state: paused;
  }
  
  @include mobile-only {
    bottom: $spacing-4;
    right: $spacing-4;
    width: 48px;
    height: 48px;
    font-size: $font-size-lg;
  }
}

.btn-fab-secondary {
  @extend .btn-fab;
  @include gradient-khmer-gold;
  
  &:hover:not(:disabled) {
    box-shadow: $shadow-khmer-gold;
  }
}

.btn-fab-accent {
  @extend .btn-fab;
  @include gradient-lotus-pink;
  
  &:hover:not(:disabled) {
    box-shadow: $shadow-lotus-pink;
  }
}

// ===== BUTTON GROUPS =====
.btn-group {
  display: inline-flex;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-base;
  
  .btn {
    border-radius: 0;
    margin: 0;
    
    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    &:first-child {
      border-top-left-radius: $border-radius-lg;
      border-bottom-left-radius: $border-radius-lg;
    }
    
    &:last-child {
      border-top-right-radius: $border-radius-lg;
      border-bottom-right-radius: $border-radius-lg;
    }
  }
}

.btn-group-vertical {
  @extend .btn-group;
  flex-direction: column;
  
  .btn {
    &:not(:last-child) {
      border-right: none;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    &:first-child {
      border-radius: $border-radius-lg $border-radius-lg 0 0;
    }
    
    &:last-child {
      border-radius: 0 0 $border-radius-lg $border-radius-lg;
    }
  }
}

// ===== ANIMATED BUTTONS =====
.btn-shimmer {
  @include shimmer-effect;
  overflow: hidden;
}

.btn-pulse {
  @include pulse-animation;
}

.btn-glow {
  &:hover:not(:disabled) {
    animation: pulse-glow 1s ease-in-out infinite;
  }
}

// ===== ICON BUTTONS =====
.btn-icon-only {
  padding: $spacing-3;
  
  &.btn-sm {
    padding: $spacing-2;
  }
  
  &.btn-lg {
    padding: $spacing-4;
  }
}

// ===== RESPONSIVE BUTTONS =====
@include mobile-only {
  .btn {
    padding: $spacing-2 $spacing-4;
    font-size: $font-size-sm;
  }
  
  .btn-lg {
    padding: $spacing-3 $spacing-6;
    font-size: $font-size-base;
  }
  
  .btn-group {
    flex-direction: column;
    width: 100%;
    
    .btn {
      &:not(:last-child) {
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      }
      
      &:first-child {
        border-radius: $border-radius-lg $border-radius-lg 0 0;
      }
      
      &:last-child {
        border-radius: 0 0 $border-radius-lg $border-radius-lg;
      }
    }
  }
}

// ===== BUTTON ANIMATIONS =====
.btn-bounce {
  &:active {
    animation: bounce-in 0.3s ease-out;
  }
}

.btn-ripple {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }
  
  &:active::before {
    width: 300px;
    height: 300px;
  }
}
