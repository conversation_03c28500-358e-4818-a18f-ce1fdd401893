import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { Loan, Client, Payment, DashboardStats } from '../types'

// ===== TYPES =====
interface LoanState {
  // Data
  loans: Loan[]
  clients: Client[]
  payments: Payment[]
  dashboardStats: DashboardStats | null
  
  // UI State
  isLoading: boolean
  error: string | null
  selectedLoan: Loan | null
  selectedClient: Client | null
  
  // Filters
  loanFilter: 'all' | 'active' | 'overdue' | 'completed'
  searchQuery: string
  
  // Actions
  setLoans: (loans: Loan[]) => void
  addLoan: (loan: Loan) => void
  updateLoan: (id: string, updates: Partial<Loan>) => void
  deleteLoan: (id: string) => void
  
  setClients: (clients: Client[]) => void
  addClient: (client: Client) => void
  updateClient: (id: string, updates: Partial<Client>) => void
  deleteClient: (id: string) => void
  
  setPayments: (payments: Payment[]) => void
  addPayment: (payment: Payment) => void
  
  setDashboardStats: (stats: DashboardStats) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setSelectedLoan: (loan: Loan | null) => void
  setSelectedClient: (client: Client | null) => void
  setLoanFilter: (filter: 'all' | 'active' | 'overdue' | 'completed') => void
  setSearchQuery: (query: string) => void
  
  // Computed
  getFilteredLoans: () => Loan[]
  getOverdueLoans: () => Loan[]
  getClientLoans: (clientId: string) => Loan[]
}

// ===== LOAN STORE =====
export const useLoanStore = create<LoanState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    loans: [],
    clients: [],
    payments: [],
    dashboardStats: null,
    isLoading: false,
    error: null,
    selectedLoan: null,
    selectedClient: null,
    loanFilter: 'all',
    searchQuery: '',

    // Loan actions
    setLoans: (loans) => set({ loans }),
    
    addLoan: (loan) => set((state) => ({
      loans: [...state.loans, loan]
    })),
    
    updateLoan: (id, updates) => set((state) => ({
      loans: state.loans.map(loan => 
        loan.id === id ? { ...loan, ...updates } : loan
      )
    })),
    
    deleteLoan: (id) => set((state) => ({
      loans: state.loans.filter(loan => loan.id !== id)
    })),

    // Client actions
    setClients: (clients) => set({ clients }),
    
    addClient: (client) => set((state) => ({
      clients: [...state.clients, client]
    })),
    
    updateClient: (id, updates) => set((state) => ({
      clients: state.clients.map(client => 
        client.id === id ? { ...client, ...updates } : client
      )
    })),
    
    deleteClient: (id) => set((state) => ({
      clients: state.clients.filter(client => client.id !== id)
    })),

    // Payment actions
    setPayments: (payments) => set({ payments }),
    
    addPayment: (payment) => set((state) => ({
      payments: [...state.payments, payment]
    })),

    // UI actions
    setDashboardStats: (stats) => set({ dashboardStats: stats }),
    setLoading: (loading) => set({ isLoading: loading }),
    setError: (error) => set({ error }),
    setSelectedLoan: (loan) => set({ selectedLoan: loan }),
    setSelectedClient: (client) => set({ selectedClient: client }),
    setLoanFilter: (filter) => set({ loanFilter: filter }),
    setSearchQuery: (query) => set({ searchQuery: query }),

    // Computed getters
    getFilteredLoans: () => {
      const { loans, loanFilter, searchQuery } = get()
      let filtered = loans

      // Apply status filter
      if (loanFilter !== 'all') {
        filtered = filtered.filter(loan => loan.status === loanFilter)
      }

      // Apply search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        filtered = filtered.filter(loan => 
          loan.clientName.toLowerCase().includes(query) ||
          loan.id.toLowerCase().includes(query)
        )
      }

      return filtered
    },

    getOverdueLoans: () => {
      const { loans } = get()
      const today = new Date()
      return loans.filter(loan => 
        loan.status === 'active' && 
        new Date(loan.dueDate) < today
      )
    },

    getClientLoans: (clientId: string) => {
      const { loans } = get()
      return loans.filter(loan => loan.clientId === clientId)
    }
  }))
)

// ===== SELECTORS =====
export const useLoans = () => useLoanStore((state) => state.loans)
export const useClients = () => useLoanStore((state) => state.clients)
export const useFilteredLoans = () => useLoanStore((state) => state.getFilteredLoans())
export const useOverdueLoans = () => useLoanStore((state) => state.getOverdueLoans())
export const useDashboardStats = () => useLoanStore((state) => state.dashboardStats)
export const useLoanLoading = () => useLoanStore((state) => state.isLoading)
export const useLoanError = () => useLoanStore((state) => state.error)
