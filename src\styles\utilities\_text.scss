// Text Utilities

// ===== TEXT ALIGNMENT =====
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// ===== TEXT COLORS =====
.text-primary { color: $primary; }
.text-secondary { color: $secondary; }
.text-accent { color: $accent; }
.text-success { color: $success; }
.text-warning { color: $warning; }
.text-danger { color: $danger; }
.text-info { color: $info; }

.text-white { color: $white; }
.text-black { color: $black; }

.text-gray-50 { color: $gray-50; }
.text-gray-100 { color: $gray-100; }
.text-gray-200 { color: $gray-200; }
.text-gray-300 { color: $gray-300; }
.text-gray-400 { color: $gray-400; }
.text-gray-500 { color: $gray-500; }
.text-gray-600 { color: $gray-600; }
.text-gray-700 { color: $gray-700; }
.text-gray-800 { color: $gray-800; }
.text-gray-900 { color: $gray-900; }

.text-khmer-gold-400 { color: $khmer-gold-400; }
.text-khmer-gold-500 { color: $khmer-gold-500; }
.text-khmer-gold-600 { color: $khmer-gold-600; }

.text-temple-blue-400 { color: $temple-blue-400; }
.text-temple-blue-500 { color: $temple-blue-500; }
.text-temple-blue-600 { color: $temple-blue-600; }

.text-lotus-pink-400 { color: $lotus-pink-400; }
.text-lotus-pink-500 { color: $lotus-pink-500; }
.text-lotus-pink-600 { color: $lotus-pink-600; }

// ===== TEXT SIZES =====
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }
.text-3xl { font-size: $font-size-3xl; }
.text-4xl { font-size: $font-size-4xl; }

// ===== FONT WEIGHTS =====
.font-light { font-weight: $font-weight-light; }
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-bold { font-weight: $font-weight-bold; }
.font-extrabold { font-weight: $font-weight-extrabold; }

// ===== LINE HEIGHTS =====
.leading-tight { line-height: $line-height-tight; }
.leading-normal { line-height: $line-height-normal; }
.leading-relaxed { line-height: $line-height-relaxed; }

// ===== TEXT TRANSFORM =====
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

// ===== TEXT DECORATION =====
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

// ===== FONT STYLE =====
.italic { font-style: italic; }
.not-italic { font-style: normal; }

// ===== LETTER SPACING =====
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

// ===== TEXT OVERFLOW =====
.truncate {
  @include truncate;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

// ===== WHITESPACE =====
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

// ===== WORD BREAK =====
.break-normal {
  overflow-wrap: normal;
  word-break: normal;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

// ===== TEXT SELECTION =====
.select-none {
  user-select: none;
}

.select-text {
  user-select: text;
}

.select-all {
  user-select: all;
}

.select-auto {
  user-select: auto;
}

// ===== VERTICAL ALIGNMENT =====
.align-baseline { vertical-align: baseline; }
.align-top { vertical-align: top; }
.align-middle { vertical-align: middle; }
.align-bottom { vertical-align: bottom; }
.align-text-top { vertical-align: text-top; }
.align-text-bottom { vertical-align: text-bottom; }

// ===== RESPONSIVE TEXT UTILITIES =====
@include mobile-only {
  .text-xs-mobile { font-size: $font-size-xs; }
  .text-sm-mobile { font-size: $font-size-sm; }
  .text-base-mobile { font-size: $font-size-base; }
  .text-lg-mobile { font-size: $font-size-lg; }
  .text-xl-mobile { font-size: $font-size-xl; }
  .text-2xl-mobile { font-size: $font-size-2xl; }
  
  .text-center-mobile { text-align: center; }
  .text-left-mobile { text-align: left; }
  .text-right-mobile { text-align: right; }
}

@include tablet-up {
  .text-xs-tablet { font-size: $font-size-xs; }
  .text-sm-tablet { font-size: $font-size-sm; }
  .text-base-tablet { font-size: $font-size-base; }
  .text-lg-tablet { font-size: $font-size-lg; }
  .text-xl-tablet { font-size: $font-size-xl; }
  .text-2xl-tablet { font-size: $font-size-2xl; }
  .text-3xl-tablet { font-size: $font-size-3xl; }
  
  .text-center-tablet { text-align: center; }
  .text-left-tablet { text-align: left; }
  .text-right-tablet { text-align: right; }
}

@include desktop-up {
  .text-xs-desktop { font-size: $font-size-xs; }
  .text-sm-desktop { font-size: $font-size-sm; }
  .text-base-desktop { font-size: $font-size-base; }
  .text-lg-desktop { font-size: $font-size-lg; }
  .text-xl-desktop { font-size: $font-size-xl; }
  .text-2xl-desktop { font-size: $font-size-2xl; }
  .text-3xl-desktop { font-size: $font-size-3xl; }
  .text-4xl-desktop { font-size: $font-size-4xl; }
  
  .text-center-desktop { text-align: center; }
  .text-left-desktop { text-align: left; }
  .text-right-desktop { text-align: right; }
}
