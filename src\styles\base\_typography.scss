// Typography System with Google Fonts Integration

// ===== GOOGLE FONTS IMPORT =====
@import url('https://fonts.googleapis.com/css2?family=Kantumruy+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap');

// ===== FONT FACE DECLARATIONS =====
// Preload critical font weights for better performance
@font-face {
  font-family: 'Kantumruy Pro';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/kantumruypro/v8/1q2TY5aECkp34vEBSPFOmJxwvk_pilU.woff2') format('woff2');
  unicode-range: U+1780-17FF, U+19E0-19FF, U+200C-200D, U+25CC;
}

@font-face {
  font-family: 'Kantumruy Pro';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/kantumruypro/v8/1q2TY5aECkp34vEBSPFOmJxwvk_pilU.woff2') format('woff2');
  unicode-range: U+1780-17FF, U+19E0-19FF, U+200C-200D, U+25CC;
}

@font-face {
  font-family: 'Kantumruy Pro';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/kantumruypro/v8/1q2TY5aECkp34vEBSPFOmJxwvk_pilU.woff2') format('woff2');
  unicode-range: U+1780-17FF, U+19E0-19FF, U+200C-200D, U+25CC;
}

// ===== TYPOGRAPHY CLASSES =====

// Display text styles
.text-display-1 {
  font-size: $font-size-4xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  letter-spacing: -0.025em;
  @include text-shadow-soft;
  @include khmer-text;
  
  @include mobile-only {
    font-size: $font-size-3xl;
  }
}

.text-display-2 {
  font-size: $font-size-3xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  letter-spacing: -0.025em;
  @include text-shadow-soft;
  @include khmer-text;
  
  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

// Heading styles
.text-heading-1 {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  @include khmer-text;
  
  @include mobile-only {
    font-size: $font-size-xl;
  }
}

.text-heading-2 {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
  @include khmer-text;
  
  @include mobile-only {
    font-size: $font-size-lg;
  }
}

.text-heading-3 {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  line-height: $line-height-normal;
  @include khmer-text;
}

// Body text styles
.text-body-large {
  font-size: $font-size-lg;
  font-weight: $font-weight-normal;
  line-height: $line-height-relaxed;
  @include khmer-text;
}

.text-body {
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
  @include khmer-text;
}

.text-body-small {
  font-size: $font-size-sm;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
  @include khmer-text;
}

// Caption and label styles
.text-caption {
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  line-height: $line-height-normal;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.text-label {
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  line-height: $line-height-normal;
  @include khmer-text;
}

// ===== GRADIENT TEXT STYLES =====
.text-gradient-khmer-gold {
  @include text-gradient($gradient-khmer-gold);
  font-weight: $font-weight-bold;
}

.text-gradient-temple-blue {
  @include text-gradient($gradient-temple-blue);
  font-weight: $font-weight-bold;
}

.text-gradient-lotus-pink {
  @include text-gradient($gradient-lotus-pink);
  font-weight: $font-weight-bold;
}

.text-gradient-sunset {
  @include text-gradient($gradient-sunset);
  font-weight: $font-weight-bold;
}

// ===== TEXT SHADOW STYLES =====
.text-shadow-soft {
  @include text-shadow-soft;
}

.text-shadow-strong {
  @include text-shadow-strong;
}

.text-shadow-khmer-gold {
  text-shadow: 0 2px 4px rgba($khmer-gold-500, 0.3);
}

.text-shadow-temple-blue {
  text-shadow: 0 2px 4px rgba($temple-blue-500, 0.3);
}

.text-shadow-lotus-pink {
  text-shadow: 0 2px 4px rgba($lotus-pink-500, 0.3);
}

// ===== KHMER SPECIFIC STYLES =====
.khmer-text {
  @include khmer-text;
}

.khmer-title {
  @include khmer-text;
  font-weight: $font-weight-bold;
  font-size: $font-size-2xl;
  line-height: $line-height-tight;
  @include text-shadow-soft;
  
  @include mobile-only {
    font-size: $font-size-xl;
  }
}

.khmer-subtitle {
  @include khmer-text;
  font-weight: $font-weight-semibold;
  font-size: $font-size-lg;
  line-height: $line-height-normal;
  color: $gray-600;
}

// ===== RESPONSIVE TYPOGRAPHY =====
.text-responsive {
  font-size: fluid-type($font-size-sm, $font-size-lg);
  line-height: $line-height-normal;
}

.text-responsive-large {
  font-size: fluid-type($font-size-lg, $font-size-2xl);
  line-height: $line-height-tight;
}

// ===== UTILITY CLASSES =====
.text-uppercase {
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

.text-normal-case {
  text-transform: none;
}

.text-italic {
  font-style: italic;
}

.text-not-italic {
  font-style: normal;
}

.text-underline {
  text-decoration: underline;
}

.text-line-through {
  text-decoration: line-through;
}

.text-no-underline {
  text-decoration: none;
}

// Font weights
.font-thin {
  font-weight: $font-weight-light;
}

.font-normal {
  font-weight: $font-weight-normal;
}

.font-medium {
  font-weight: $font-weight-medium;
}

.font-semibold {
  font-weight: $font-weight-semibold;
}

.font-bold {
  font-weight: $font-weight-bold;
}

.font-extrabold {
  font-weight: $font-weight-extrabold;
}

// Line heights
.leading-tight {
  line-height: $line-height-tight;
}

.leading-normal {
  line-height: $line-height-normal;
}

.leading-relaxed {
  line-height: $line-height-relaxed;
}

// Letter spacing
.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-normal {
  letter-spacing: 0;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

// ===== ANIMATED TEXT EFFECTS =====
.text-shimmer {
  @include shimmer-effect;
  background: linear-gradient(
    90deg,
    $gray-400 25%,
    $gray-200 50%,
    $gray-400 75%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  animation: text-glow 2s ease-in-out infinite alternate;
}

@keyframes text-glow {
  from {
    text-shadow: 0 0 5px rgba($primary, 0.5);
  }
  to {
    text-shadow: 0 0 20px rgba($primary, 0.8), 0 0 30px rgba($primary, 0.6);
  }
}

// ===== READING OPTIMIZATION =====
.text-optimized {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  font-variant-ligatures: common-ligatures;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
