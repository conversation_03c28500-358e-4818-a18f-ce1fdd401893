import { useCallback, useState, useEffect } from '@lynx-js/react'
import { useTheme, ThemeMode } from '../../contexts/ThemeContext'

// ===== TYPES =====
interface ThemeToggleProps {
  size?: 'small' | 'medium' | 'large'
  showLabel?: boolean
  showIndicator?: boolean
  className?: string
  style?: any
  language?: 'en' | 'km'
}

// ===== CONSTANTS =====
const SIZES = {
  small: { width: 48, height: 24, iconSize: 12 },
  medium: { width: 60, height: 32, iconSize: 16 },
  large: { width: 72, height: 40, iconSize: 20 }
}

const LABELS = {
  en: {
    light: 'Light Mode',
    dark: 'Dark Mode',
    system: 'System Theme',
    toggle: 'Toggle Theme'
  },
  km: {
    light: 'ពន្លឺ',
    dark: 'ងងឹត',
    system: 'ប្រព័ន្ធ',
    toggle: 'ប្តូរធីម'
  }
}

// ===== COMPONENT =====
export function ThemeToggle({
  size = 'medium',
  showLabel = false,
  showIndicator = false,
  className = '',
  style = {},
  language = 'en'
}: ThemeToggleProps) {
  const { mode, resolvedTheme, toggleTheme, isLoading } = useTheme()
  const [isPressed, setIsPressed] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)

  const sizeConfig = SIZES[size]
  const labels = LABELS[language]

  // Handle toggle with haptic feedback simulation
  const handleToggle = useCallback(() => {
    if (isLoading) return

    setIsPressed(true)
    toggleTheme()

    // Reset pressed state
    setTimeout(() => setIsPressed(false), 150)

    // Show indicator briefly
    if (showIndicator) {
      setShowTooltip(true)
      setTimeout(() => setShowTooltip(false), 2000)
    }
  }, [toggleTheme, isLoading, showIndicator])

  // Get current theme icon
  const getThemeIcon = () => {
    if (mode === 'system') {
      return resolvedTheme === 'dark' ? '🌙' : '☀️'
    }
    return resolvedTheme === 'dark' ? '🌙' : '☀️'
  }

  // Get theme label
  const getThemeLabel = () => {
    if (mode === 'system') return labels.system
    return resolvedTheme === 'dark' ? labels.dark : labels.light
  }

  // Loading state
  if (isLoading) {
    return (
      <view style={{
        width: sizeConfig.width,
        height: sizeConfig.height,
        backgroundColor: '#e0e0e0',
        borderRadius: sizeConfig.height / 2,
        ...style
      }} className={`theme-toggle-loading ${className}`}>
        <view style={{
          width: sizeConfig.height - 4,
          height: sizeConfig.height - 4,
          backgroundColor: '#f0f0f0',
          borderRadius: '50%',
          margin: 2,
          animation: 'pulse 1.5s ease-in-out infinite'
        }} />
      </view>
    )
  }

  return (
    <view style={{ position: 'relative', ...style }} className={className}>
      {/* Toggle Button */}
      <view
        style={{
          width: sizeConfig.width,
          height: sizeConfig.height,
          backgroundColor: resolvedTheme === 'dark' ? '#4f46e5' : '#e5e7eb',
          borderRadius: sizeConfig.height / 2,
          position: 'relative',
          cursor: 'pointer',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: isPressed ? 'scale(0.95)' : 'scale(1)',
          boxShadow: resolvedTheme === 'dark'
            ? '0 4px 14px 0 rgba(79, 70, 229, 0.3)'
            : '0 4px 14px 0 rgba(0, 0, 0, 0.1)'
        }}
        className={`theme-toggle ${resolvedTheme === 'dark' ? 'theme-toggle-dark' : 'theme-toggle-light'}`}
        bindtap={handleToggle}
      >
        {/* Toggle Indicator */}
        <view
          style={{
            position: 'absolute',
            top: 2,
            left: resolvedTheme === 'dark' ? sizeConfig.width - sizeConfig.height + 2 : 2,
            width: sizeConfig.height - 4,
            height: sizeConfig.height - 4,
            backgroundColor: '#ffffff',
            borderRadius: '50%',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <text style={{
            fontSize: sizeConfig.iconSize,
            lineHeight: 1
          }}>
            {getThemeIcon()}
          </text>
        </view>

        {/* System Mode Indicator */}
        {mode === 'system' && (
          <view
            style={{
              position: 'absolute',
              top: -2,
              right: -2,
              width: 8,
              height: 8,
              backgroundColor: '#10b981',
              borderRadius: '50%',
              border: '2px solid #ffffff',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)'
            }}
          />
        )}
      </view>

      {/* Label */}
      {showLabel && (
        <text style={{
          marginTop: 8,
          fontSize: 12,
          color: resolvedTheme === 'dark' ? '#d1d5db' : '#6b7280',
          textAlign: 'center',
          fontWeight: '500'
        }}>
          {getThemeLabel()}
        </text>
      )}

      {/* Tooltip */}
      {showTooltip && (
        <view
          style={{
            position: 'absolute',
            bottom: '100%',
            left: '50%',
            transform: 'translateX(-50%)',
            marginBottom: 8,
            padding: '6px 12px',
            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#1f2937',
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            whiteSpace: 'nowrap',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            zIndex: 1000,
            opacity: showTooltip ? 1 : 0,
            transition: 'opacity 0.2s ease-in-out'
          }}
          className="theme-toggle-tooltip"
        >
          <text>{labels.toggle}</text>

          {/* Tooltip Arrow */}
          <view
            style={{
              position: 'absolute',
              top: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              width: 0,
              height: 0,
              borderLeft: '4px solid transparent',
              borderRight: '4px solid transparent',
              borderTop: `4px solid ${resolvedTheme === 'dark' ? '#374151' : '#1f2937'}`
            }}
          />
        </view>
      )}

      {/* Theme Indicator */}
      {showIndicator && showTooltip && (
        <view
          style={{
            position: 'fixed',
            top: 20,
            right: 20,
            padding: '8px 12px',
            backgroundColor: resolvedTheme === 'dark'
              ? 'rgba(55, 65, 81, 0.9)'
              : 'rgba(255, 255, 255, 0.9)',
            color: resolvedTheme === 'dark' ? '#ffffff' : '#1f2937',
            fontSize: 12,
            borderRadius: 8,
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(8px)',
            border: `1px solid ${resolvedTheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            gap: 8,
            animation: 'slideInRight 0.3s ease-out'
          }}
          className="theme-indicator theme-indicator-visible"
        >
          <view
            style={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: resolvedTheme === 'dark' ? '#4f46e5' : '#10b981'
            }}
          />
          <text>{getThemeLabel()}</text>
        </view>
      )}
    </view>
  )
}

// ===== THEME TOGGLE MENU =====
interface ThemeToggleMenuProps {
  language?: 'en' | 'km'
  onClose?: () => void
}

export function ThemeToggleMenu({ language = 'en', onClose }: ThemeToggleMenuProps) {
  const { mode, setTheme } = useTheme()
  const labels = LABELS[language]

  const options: { value: ThemeMode; label: string; icon: string }[] = [
    { value: 'light', label: labels.light, icon: '☀️' },
    { value: 'dark', label: labels.dark, icon: '🌙' },
    { value: 'system', label: labels.system, icon: '💻' }
  ]

  const handleSelect = useCallback((selectedMode: ThemeMode) => {
    setTheme(selectedMode)
    onClose?.()
  }, [setTheme, onClose])

  return (
    <view
      style={{
        backgroundColor: 'var(--bg-primary)',
        border: '1px solid var(--border-primary)',
        borderRadius: 8,
        padding: 8,
        boxShadow: '0 4px 6px var(--shadow-color)',
        minWidth: 120
      }}
      className="theme-toggle-menu"
    >
      {options.map((option) => (
        <view
          key={option.value}
          style={{
            padding: '8px 12px',
            borderRadius: 4,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: 8,
            backgroundColor: mode === option.value ? 'var(--color-primary-light)' : 'transparent',
            color: mode === option.value ? 'var(--color-primary)' : 'var(--text-primary)',
            transition: 'all 0.2s ease-in-out'
          }}
          bindtap={() => handleSelect(option.value)}
          className={`theme-option ${mode === option.value ? 'theme-option-active' : ''}`}
        >
          <text style={{ fontSize: 16 }}>{option.icon}</text>
          <text style={{ fontSize: 14, fontWeight: mode === option.value ? '600' : '400' }}>
            {option.label}
          </text>
        </view>
      ))}
    </view>
  )
}
