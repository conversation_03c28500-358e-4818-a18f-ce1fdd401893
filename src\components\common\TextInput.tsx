import { useState, useCallback } from '@lynx-js/react'

interface TextInputProps {
  value: string
  onInput: (value: string) => void
  placeholder?: string
  type?: 'text' | 'password'
  style?: any
}

export function TextInput({ value, onInput, placeholder, type = 'text', style }: TextInputProps) {
  const [isFocused, setIsFocused] = useState(false)

  const handleFocus = useCallback(() => {
    setIsFocused(true)
  }, [])

  const handleBlur = useCallback(() => {
    setIsFocused(false)
  }, [])

  const handleInput = useCallback((e: any) => {
    onInput(e.detail.value || '')
  }, [onInput])

  return (
    <view style={{
      borderWidth: 1,
      borderColor: isFocused ? '#007bff' : '#ddd',
      borderRadius: 8,
      backgroundColor: '#fff',
      ...style
    }}>
      <text
        style={{
          padding: 12,
          fontSize: 16,
          color: value ? '#333' : '#999',
          minHeight: 20
        }}
        bindtap={handleFocus}
      >
        {value || placeholder || ''}
      </text>
    </view>
  )
}
