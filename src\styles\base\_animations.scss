// Advanced Animations for Khmer Loan Management App

// ===== FLOATING ANIMATIONS =====
@keyframes floating {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes floating-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes floating-fast {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

// ===== SHIMMER EFFECTS =====
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes shimmer-gold {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// ===== PULSE ANIMATIONS =====
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba($primary, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba($primary, 0.8), 0 0 30px rgba($primary, 0.6);
  }
}

// ===== BOUNCE ANIMATIONS =====
@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce-out {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.3);
    opacity: 0;
  }
}

// ===== SLIDE ANIMATIONS =====
@keyframes slide-in-up {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-down {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

// ===== FADE ANIMATIONS =====
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fade-in-scale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// ===== ROTATION ANIMATIONS =====
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotate-reverse {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

// ===== SPARKLE ANIMATIONS =====
@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

// ===== GRADIENT ANIMATIONS =====
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-khmer {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// ===== STAGGER ANIMATIONS =====
@keyframes stagger-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// ===== ANIMATION UTILITY CLASSES =====

// Floating animations
.animate-floating {
  animation: floating 3s ease-in-out infinite;
}

.animate-floating-slow {
  animation: floating-slow 4s ease-in-out infinite;
}

.animate-floating-fast {
  animation: floating-fast 2s ease-in-out infinite;
}

// Shimmer effects
.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-shimmer-gold {
  background: linear-gradient(
    90deg,
    transparent,
    rgba($khmer-gold-400, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer-gold 2s infinite;
}

// Pulse animations
.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

// Bounce animations
.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-bounce-out {
  animation: bounce-out 0.6s ease-in;
}

// Slide animations
.animate-slide-in-up {
  animation: slide-in-up 0.5s ease-out;
}

.animate-slide-in-down {
  animation: slide-in-down 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

// Fade animations
.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-fade-out {
  animation: fade-out 0.3s ease-in;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.4s ease-out;
}

// Rotation animations
.animate-rotate {
  animation: rotate 2s linear infinite;
}

.animate-rotate-reverse {
  animation: rotate-reverse 2s linear infinite;
}

// Sparkle animations
.animate-sparkle {
  animation: sparkle 1.5s ease-in-out infinite;
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

// Gradient animations
.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-gradient-khmer {
  background: $gradient-temple;
  background-size: 200% 200%;
  animation: gradient-khmer 4s ease infinite;
}

// Stagger animations
.animate-stagger {
  animation: stagger-fade-in 0.6s ease-out;
}

// Animation delays for staggered effects
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

.animate-delay-700 {
  animation-delay: 0.7s;
}

.animate-delay-1000 {
  animation-delay: 1s;
}

// Animation durations
.animate-duration-fast {
  animation-duration: $duration-fast;
}

.animate-duration-normal {
  animation-duration: $duration-base;
}

.animate-duration-slow {
  animation-duration: $duration-slow;
}

.animate-duration-slower {
  animation-duration: $duration-slower;
}

// Animation iteration counts
.animate-once {
  animation-iteration-count: 1;
}

.animate-twice {
  animation-iteration-count: 2;
}

.animate-infinite {
  animation-iteration-count: infinite;
}

// Animation fill modes
.animate-fill-forwards {
  animation-fill-mode: forwards;
}

.animate-fill-backwards {
  animation-fill-mode: backwards;
}

.animate-fill-both {
  animation-fill-mode: both;
}

// Animation play states
.animate-paused {
  animation-play-state: paused;
}

.animate-running {
  animation-play-state: running;
}

// ===== HOVER ANIMATIONS =====
.hover-lift {
  @include hover-lift;
}

.hover-scale {
  @include hover-scale;
}

.hover-glow {
  @include hover-glow;
}

.hover-glow-khmer-gold {
  @include hover-glow($khmer-gold-500);
}

.hover-glow-temple-blue {
  @include hover-glow($temple-blue-500);
}

.hover-glow-lotus-pink {
  @include hover-glow($lotus-pink-500);
}
