// Dashboard-Specific Components

// ===== DASHBOARD LAYOUT =====
.dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, $gray-50 0%, $gray-100 100%);
  position: relative;
  overflow-x: hidden;
  
  // Khmer pattern background
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba($khmer-gold-200, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba($temple-blue-200, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba($lotus-pink-200, 0.05) 0%, transparent 50%);
    background-size: 400px 400px, 600px 600px, 800px 800px;
    background-position: 0 0, 200px 200px, 400px 400px;
    animation: float-pattern 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
  }
}

@keyframes float-pattern {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(1deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(-1deg);
  }
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-6;
  position: relative;
  z-index: 1;
  
  @include mobile-only {
    padding: $spacing-4;
  }
}

// ===== DASHBOARD HEADER =====
.dashboard-header {
  margin-bottom: $spacing-8;
  text-align: center;
  
  .dashboard-title {
    font-size: $font-size-4xl;
    font-weight: $font-weight-bold;
    @include text-gradient($gradient-temple);
    margin-bottom: $spacing-2;
    @include khmer-text;
    @include floating-animation;
    
    @include mobile-only {
      font-size: $font-size-3xl;
    }
  }
  
  .dashboard-subtitle {
    font-size: $font-size-lg;
    color: $gray-600;
    @include khmer-text;
    @include text-shadow-soft;
  }
  
  .dashboard-date {
    font-size: $font-size-sm;
    color: $gray-500;
    margin-top: $spacing-2;
    @include khmer-text;
  }
}

// ===== DASHBOARD SECTIONS =====
.dashboard-section {
  margin-bottom: $spacing-10;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.dashboard-section-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-800;
  margin-bottom: $spacing-6;
  @include khmer-text;
  @include text-shadow-soft;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -$spacing-2;
    left: 0;
    width: 60px;
    height: 3px;
    background: $gradient-khmer-gold;
    border-radius: $border-radius-full;
  }
  
  @include mobile-only {
    font-size: $font-size-xl;
  }
}

// ===== STATS GRID =====
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: $spacing-6;
  margin-bottom: $spacing-8;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.stat-card-enhanced {
  @include card-glass;
  text-align: center;
  position: relative;
  overflow: hidden;
  padding: $spacing-8;
  @include hover-lift;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $gradient-temple;
    border-radius: $border-radius-xl $border-radius-xl 0 0;
  }
  
  &.stat-card-khmer-gold::before {
    background: $gradient-khmer-gold;
  }
  
  &.stat-card-temple-blue::before {
    background: $gradient-temple-blue;
  }
  
  &.stat-card-lotus-pink::before {
    background: $gradient-lotus-pink;
  }
  
  &.stat-card-sunset::before {
    background: $gradient-sunset;
  }
  
  .stat-icon {
    font-size: $font-size-4xl;
    margin-bottom: $spacing-4;
    @include floating-animation;
    
    &.stat-icon-khmer-gold {
      color: $khmer-gold-500;
      @include text-shadow-soft;
    }
    
    &.stat-icon-temple-blue {
      color: $temple-blue-500;
      @include text-shadow-soft;
    }
    
    &.stat-icon-lotus-pink {
      color: $lotus-pink-500;
      @include text-shadow-soft;
    }
  }
  
  .stat-value {
    font-size: $font-size-4xl;
    font-weight: $font-weight-extrabold;
    color: $gray-900;
    margin-bottom: $spacing-2;
    @include text-shadow-soft;
    
    @include mobile-only {
      font-size: $font-size-3xl;
    }
  }
  
  .stat-label {
    font-size: $font-size-base;
    color: $gray-600;
    font-weight: $font-weight-medium;
    @include khmer-text;
  }
  
  .stat-change {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    margin-top: $spacing-2;
    
    &.stat-change-positive {
      color: $success;
      
      &::before {
        content: '↗ ';
      }
    }
    
    &.stat-change-negative {
      color: $danger;
      
      &::before {
        content: '↘ ';
      }
    }
  }
}

// ===== QUICK ACTIONS GRID =====
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-4;
  margin-bottom: $spacing-8;
  
  @include mobile-only {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-3;
  }
}

.quick-action-card {
  @include card-glass;
  @include flex-column-center;
  padding: $spacing-6;
  cursor: pointer;
  transition: all $transition-base;
  min-height: 140px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba($primary, 0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform $transition-slow;
  }
  
  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: $shadow-xl;
    
    &::before {
      transform: scale(1);
    }
    
    .quick-action-icon {
      animation-play-state: paused;
      transform: scale(1.2);
    }
  }
  
  &:active {
    transform: translateY(-4px) scale(1);
  }
  
  .quick-action-icon {
    font-size: $font-size-4xl;
    margin-bottom: $spacing-3;
    color: $primary;
    @include floating-animation;
    transition: transform $transition-base;
    
    @include mobile-only {
      font-size: $font-size-3xl;
      margin-bottom: $spacing-2;
    }
  }
  
  .quick-action-title {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $gray-800;
    text-align: center;
    @include khmer-text;
    
    @include mobile-only {
      font-size: $font-size-sm;
    }
  }
}

// ===== ACTIVITY FEED =====
.activity-feed {
  @include card-glass;
  padding: $spacing-6;
  max-height: 400px;
  overflow-y: auto;
}

.activity-feed-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $gray-800;
  margin-bottom: $spacing-6;
  @include khmer-text;
  @include text-shadow-soft;
}

.activity-item-enhanced {
  display: flex;
  align-items: flex-start;
  padding: $spacing-4 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all $transition-base;
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
  
  &:hover {
    transform: translateX(8px);
    background: rgba(255, 255, 255, 0.1);
    border-radius: $border-radius-lg;
    padding-left: $spacing-4;
    padding-right: $spacing-4;
  }
  
  .activity-indicator-enhanced {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: $spacing-4;
    margin-top: $spacing-1;
    position: relative;
    
    &.activity-indicator-primary {
      background: $primary;
      @include pulse-animation;
    }
    
    &.activity-indicator-success {
      background: $success;
    }
    
    &.activity-indicator-warning {
      background: $warning;
    }
    
    &.activity-indicator-danger {
      background: $danger;
    }
    
    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid currentColor;
      border-radius: 50%;
      opacity: 0.3;
      animation: ripple 2s infinite;
    }
  }
  
  .activity-content-enhanced {
    flex: 1;
    
    .activity-title-enhanced {
      font-size: $font-size-base;
      font-weight: $font-weight-semibold;
      color: $gray-800;
      margin-bottom: $spacing-1;
      @include khmer-text;
    }
    
    .activity-subtitle-enhanced {
      font-size: $font-size-sm;
      color: $gray-600;
      margin-bottom: $spacing-1;
      @include khmer-text;
    }
    
    .activity-time-enhanced {
      font-size: $font-size-xs;
      color: $gray-500;
      font-weight: $font-weight-medium;
    }
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// ===== PROGRESS INDICATORS =====
.progress-card {
  @include card-glass;
  padding: $spacing-6;
  
  .progress-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-800;
    margin-bottom: $spacing-4;
    @include khmer-text;
  }
  
  .progress-bar-container {
    position: relative;
    height: 12px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: $border-radius-full;
    overflow: hidden;
    margin-bottom: $spacing-2;
    
    .progress-bar {
      height: 100%;
      background: $gradient-temple-blue;
      border-radius: $border-radius-full;
      transition: width $transition-slow;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        animation: shimmer 2s infinite;
      }
      
      &.progress-bar-success {
        background: linear-gradient(135deg, $success 0%, darken($success, 10%) 100%);
      }
      
      &.progress-bar-warning {
        background: $gradient-khmer-gold;
      }
      
      &.progress-bar-danger {
        background: linear-gradient(135deg, $danger 0%, darken($danger, 10%) 100%);
      }
    }
  }
  
  .progress-text {
    display: flex;
    justify-content: space-between;
    font-size: $font-size-sm;
    color: $gray-600;
  }
}

// ===== FLOATING ELEMENTS =====
.floating-sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: $khmer-gold-400;
  border-radius: 50%;
  @include sparkle-animation;
  pointer-events: none;
  
  &:nth-child(1) {
    top: 10%;
    left: 20%;
    animation-delay: 0s;
  }
  
  &:nth-child(2) {
    top: 30%;
    right: 15%;
    animation-delay: 1s;
  }
  
  &:nth-child(3) {
    bottom: 20%;
    left: 10%;
    animation-delay: 2s;
  }
  
  &:nth-child(4) {
    bottom: 40%;
    right: 25%;
    animation-delay: 3s;
  }
}

// ===== RESPONSIVE DASHBOARD =====
@include mobile-only {
  .dashboard-container {
    padding: $spacing-3;
  }
  
  .dashboard-section {
    margin-bottom: $spacing-6;
  }
  
  .stats-grid {
    margin-bottom: $spacing-6;
  }
  
  .quick-actions-grid {
    margin-bottom: $spacing-6;
  }
  
  .stat-card-enhanced {
    padding: $spacing-6;
  }
  
  .quick-action-card {
    padding: $spacing-4;
    min-height: 120px;
  }
  
  .activity-feed {
    padding: $spacing-4;
    max-height: 300px;
  }
}
