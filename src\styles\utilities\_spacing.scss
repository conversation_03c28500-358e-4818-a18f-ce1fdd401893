// Spacing Utilities

// ===== MARGIN UTILITIES =====

// All sides
.m-0 { margin: 0; }
.m-1 { margin: $spacing-1; }
.m-2 { margin: $spacing-2; }
.m-3 { margin: $spacing-3; }
.m-4 { margin: $spacing-4; }
.m-5 { margin: $spacing-5; }
.m-6 { margin: $spacing-6; }
.m-8 { margin: $spacing-8; }
.m-10 { margin: $spacing-10; }
.m-12 { margin: $spacing-12; }
.m-16 { margin: $spacing-16; }
.m-20 { margin: $spacing-20; }
.m-auto { margin: auto; }

// Top
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: $spacing-1; }
.mt-2 { margin-top: $spacing-2; }
.mt-3 { margin-top: $spacing-3; }
.mt-4 { margin-top: $spacing-4; }
.mt-5 { margin-top: $spacing-5; }
.mt-6 { margin-top: $spacing-6; }
.mt-8 { margin-top: $spacing-8; }
.mt-10 { margin-top: $spacing-10; }
.mt-12 { margin-top: $spacing-12; }
.mt-16 { margin-top: $spacing-16; }
.mt-20 { margin-top: $spacing-20; }
.mt-auto { margin-top: auto; }

// Right
.mr-0 { margin-right: 0; }
.mr-1 { margin-right: $spacing-1; }
.mr-2 { margin-right: $spacing-2; }
.mr-3 { margin-right: $spacing-3; }
.mr-4 { margin-right: $spacing-4; }
.mr-5 { margin-right: $spacing-5; }
.mr-6 { margin-right: $spacing-6; }
.mr-8 { margin-right: $spacing-8; }
.mr-10 { margin-right: $spacing-10; }
.mr-12 { margin-right: $spacing-12; }
.mr-16 { margin-right: $spacing-16; }
.mr-20 { margin-right: $spacing-20; }
.mr-auto { margin-right: auto; }

// Bottom
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: $spacing-1; }
.mb-2 { margin-bottom: $spacing-2; }
.mb-3 { margin-bottom: $spacing-3; }
.mb-4 { margin-bottom: $spacing-4; }
.mb-5 { margin-bottom: $spacing-5; }
.mb-6 { margin-bottom: $spacing-6; }
.mb-8 { margin-bottom: $spacing-8; }
.mb-10 { margin-bottom: $spacing-10; }
.mb-12 { margin-bottom: $spacing-12; }
.mb-16 { margin-bottom: $spacing-16; }
.mb-20 { margin-bottom: $spacing-20; }
.mb-auto { margin-bottom: auto; }

// Left
.ml-0 { margin-left: 0; }
.ml-1 { margin-left: $spacing-1; }
.ml-2 { margin-left: $spacing-2; }
.ml-3 { margin-left: $spacing-3; }
.ml-4 { margin-left: $spacing-4; }
.ml-5 { margin-left: $spacing-5; }
.ml-6 { margin-left: $spacing-6; }
.ml-8 { margin-left: $spacing-8; }
.ml-10 { margin-left: $spacing-10; }
.ml-12 { margin-left: $spacing-12; }
.ml-16 { margin-left: $spacing-16; }
.ml-20 { margin-left: $spacing-20; }
.ml-auto { margin-left: auto; }

// Horizontal (left and right)
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: $spacing-1; margin-right: $spacing-1; }
.mx-2 { margin-left: $spacing-2; margin-right: $spacing-2; }
.mx-3 { margin-left: $spacing-3; margin-right: $spacing-3; }
.mx-4 { margin-left: $spacing-4; margin-right: $spacing-4; }
.mx-5 { margin-left: $spacing-5; margin-right: $spacing-5; }
.mx-6 { margin-left: $spacing-6; margin-right: $spacing-6; }
.mx-8 { margin-left: $spacing-8; margin-right: $spacing-8; }
.mx-10 { margin-left: $spacing-10; margin-right: $spacing-10; }
.mx-12 { margin-left: $spacing-12; margin-right: $spacing-12; }
.mx-16 { margin-left: $spacing-16; margin-right: $spacing-16; }
.mx-20 { margin-left: $spacing-20; margin-right: $spacing-20; }
.mx-auto { margin-left: auto; margin-right: auto; }

// Vertical (top and bottom)
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: $spacing-1; margin-bottom: $spacing-1; }
.my-2 { margin-top: $spacing-2; margin-bottom: $spacing-2; }
.my-3 { margin-top: $spacing-3; margin-bottom: $spacing-3; }
.my-4 { margin-top: $spacing-4; margin-bottom: $spacing-4; }
.my-5 { margin-top: $spacing-5; margin-bottom: $spacing-5; }
.my-6 { margin-top: $spacing-6; margin-bottom: $spacing-6; }
.my-8 { margin-top: $spacing-8; margin-bottom: $spacing-8; }
.my-10 { margin-top: $spacing-10; margin-bottom: $spacing-10; }
.my-12 { margin-top: $spacing-12; margin-bottom: $spacing-12; }
.my-16 { margin-top: $spacing-16; margin-bottom: $spacing-16; }
.my-20 { margin-top: $spacing-20; margin-bottom: $spacing-20; }
.my-auto { margin-top: auto; margin-bottom: auto; }

// ===== PADDING UTILITIES =====

// All sides
.p-0 { padding: 0; }
.p-1 { padding: $spacing-1; }
.p-2 { padding: $spacing-2; }
.p-3 { padding: $spacing-3; }
.p-4 { padding: $spacing-4; }
.p-5 { padding: $spacing-5; }
.p-6 { padding: $spacing-6; }
.p-8 { padding: $spacing-8; }
.p-10 { padding: $spacing-10; }
.p-12 { padding: $spacing-12; }
.p-16 { padding: $spacing-16; }
.p-20 { padding: $spacing-20; }

// Top
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: $spacing-1; }
.pt-2 { padding-top: $spacing-2; }
.pt-3 { padding-top: $spacing-3; }
.pt-4 { padding-top: $spacing-4; }
.pt-5 { padding-top: $spacing-5; }
.pt-6 { padding-top: $spacing-6; }
.pt-8 { padding-top: $spacing-8; }
.pt-10 { padding-top: $spacing-10; }
.pt-12 { padding-top: $spacing-12; }
.pt-16 { padding-top: $spacing-16; }
.pt-20 { padding-top: $spacing-20; }

// Right
.pr-0 { padding-right: 0; }
.pr-1 { padding-right: $spacing-1; }
.pr-2 { padding-right: $spacing-2; }
.pr-3 { padding-right: $spacing-3; }
.pr-4 { padding-right: $spacing-4; }
.pr-5 { padding-right: $spacing-5; }
.pr-6 { padding-right: $spacing-6; }
.pr-8 { padding-right: $spacing-8; }
.pr-10 { padding-right: $spacing-10; }
.pr-12 { padding-right: $spacing-12; }
.pr-16 { padding-right: $spacing-16; }
.pr-20 { padding-right: $spacing-20; }

// Bottom
.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: $spacing-1; }
.pb-2 { padding-bottom: $spacing-2; }
.pb-3 { padding-bottom: $spacing-3; }
.pb-4 { padding-bottom: $spacing-4; }
.pb-5 { padding-bottom: $spacing-5; }
.pb-6 { padding-bottom: $spacing-6; }
.pb-8 { padding-bottom: $spacing-8; }
.pb-10 { padding-bottom: $spacing-10; }
.pb-12 { padding-bottom: $spacing-12; }
.pb-16 { padding-bottom: $spacing-16; }
.pb-20 { padding-bottom: $spacing-20; }

// Left
.pl-0 { padding-left: 0; }
.pl-1 { padding-left: $spacing-1; }
.pl-2 { padding-left: $spacing-2; }
.pl-3 { padding-left: $spacing-3; }
.pl-4 { padding-left: $spacing-4; }
.pl-5 { padding-left: $spacing-5; }
.pl-6 { padding-left: $spacing-6; }
.pl-8 { padding-left: $spacing-8; }
.pl-10 { padding-left: $spacing-10; }
.pl-12 { padding-left: $spacing-12; }
.pl-16 { padding-left: $spacing-16; }
.pl-20 { padding-left: $spacing-20; }

// Horizontal (left and right)
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: $spacing-1; padding-right: $spacing-1; }
.px-2 { padding-left: $spacing-2; padding-right: $spacing-2; }
.px-3 { padding-left: $spacing-3; padding-right: $spacing-3; }
.px-4 { padding-left: $spacing-4; padding-right: $spacing-4; }
.px-5 { padding-left: $spacing-5; padding-right: $spacing-5; }
.px-6 { padding-left: $spacing-6; padding-right: $spacing-6; }
.px-8 { padding-left: $spacing-8; padding-right: $spacing-8; }
.px-10 { padding-left: $spacing-10; padding-right: $spacing-10; }
.px-12 { padding-left: $spacing-12; padding-right: $spacing-12; }
.px-16 { padding-left: $spacing-16; padding-right: $spacing-16; }
.px-20 { padding-left: $spacing-20; padding-right: $spacing-20; }

// Vertical (top and bottom)
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: $spacing-1; padding-bottom: $spacing-1; }
.py-2 { padding-top: $spacing-2; padding-bottom: $spacing-2; }
.py-3 { padding-top: $spacing-3; padding-bottom: $spacing-3; }
.py-4 { padding-top: $spacing-4; padding-bottom: $spacing-4; }
.py-5 { padding-top: $spacing-5; padding-bottom: $spacing-5; }
.py-6 { padding-top: $spacing-6; padding-bottom: $spacing-6; }
.py-8 { padding-top: $spacing-8; padding-bottom: $spacing-8; }
.py-10 { padding-top: $spacing-10; padding-bottom: $spacing-10; }
.py-12 { padding-top: $spacing-12; padding-bottom: $spacing-12; }
.py-16 { padding-top: $spacing-16; padding-bottom: $spacing-16; }
.py-20 { padding-top: $spacing-20; padding-bottom: $spacing-20; }

// ===== GAP UTILITIES =====
.gap-0 { gap: 0; }
.gap-1 { gap: $spacing-1; }
.gap-2 { gap: $spacing-2; }
.gap-3 { gap: $spacing-3; }
.gap-4 { gap: $spacing-4; }
.gap-5 { gap: $spacing-5; }
.gap-6 { gap: $spacing-6; }
.gap-8 { gap: $spacing-8; }
.gap-10 { gap: $spacing-10; }
.gap-12 { gap: $spacing-12; }
.gap-16 { gap: $spacing-16; }
.gap-20 { gap: $spacing-20; }

// Row gap
.gap-y-0 { row-gap: 0; }
.gap-y-1 { row-gap: $spacing-1; }
.gap-y-2 { row-gap: $spacing-2; }
.gap-y-3 { row-gap: $spacing-3; }
.gap-y-4 { row-gap: $spacing-4; }
.gap-y-5 { row-gap: $spacing-5; }
.gap-y-6 { row-gap: $spacing-6; }
.gap-y-8 { row-gap: $spacing-8; }
.gap-y-10 { row-gap: $spacing-10; }
.gap-y-12 { row-gap: $spacing-12; }
.gap-y-16 { row-gap: $spacing-16; }
.gap-y-20 { row-gap: $spacing-20; }

// Column gap
.gap-x-0 { column-gap: 0; }
.gap-x-1 { column-gap: $spacing-1; }
.gap-x-2 { column-gap: $spacing-2; }
.gap-x-3 { column-gap: $spacing-3; }
.gap-x-4 { column-gap: $spacing-4; }
.gap-x-5 { column-gap: $spacing-5; }
.gap-x-6 { column-gap: $spacing-6; }
.gap-x-8 { column-gap: $spacing-8; }
.gap-x-10 { column-gap: $spacing-10; }
.gap-x-12 { column-gap: $spacing-12; }
.gap-x-16 { column-gap: $spacing-16; }
.gap-x-20 { column-gap: $spacing-20; }

// ===== SPACE BETWEEN UTILITIES =====
.space-y-0 > * + * { margin-top: 0; }
.space-y-1 > * + * { margin-top: $spacing-1; }
.space-y-2 > * + * { margin-top: $spacing-2; }
.space-y-3 > * + * { margin-top: $spacing-3; }
.space-y-4 > * + * { margin-top: $spacing-4; }
.space-y-5 > * + * { margin-top: $spacing-5; }
.space-y-6 > * + * { margin-top: $spacing-6; }
.space-y-8 > * + * { margin-top: $spacing-8; }
.space-y-10 > * + * { margin-top: $spacing-10; }
.space-y-12 > * + * { margin-top: $spacing-12; }
.space-y-16 > * + * { margin-top: $spacing-16; }
.space-y-20 > * + * { margin-top: $spacing-20; }

.space-x-0 > * + * { margin-left: 0; }
.space-x-1 > * + * { margin-left: $spacing-1; }
.space-x-2 > * + * { margin-left: $spacing-2; }
.space-x-3 > * + * { margin-left: $spacing-3; }
.space-x-4 > * + * { margin-left: $spacing-4; }
.space-x-5 > * + * { margin-left: $spacing-5; }
.space-x-6 > * + * { margin-left: $spacing-6; }
.space-x-8 > * + * { margin-left: $spacing-8; }
.space-x-10 > * + * { margin-left: $spacing-10; }
.space-x-12 > * + * { margin-left: $spacing-12; }
.space-x-16 > * + * { margin-left: $spacing-16; }
.space-x-20 > * + * { margin-left: $spacing-20; }
