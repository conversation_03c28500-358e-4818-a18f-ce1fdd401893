// Grid Layout System

// ===== CONTAINER =====
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: $spacing-4;
  padding-right: $spacing-4;
  
  @include tablet-up {
    max-width: $breakpoint-md;
    padding-left: $spacing-6;
    padding-right: $spacing-6;
  }
  
  @include desktop-up {
    max-width: $breakpoint-lg;
    padding-left: $spacing-8;
    padding-right: $spacing-8;
  }
  
  @include large-desktop-up {
    max-width: $breakpoint-xl;
  }
}

.container-fluid {
  width: 100%;
  padding-left: $spacing-4;
  padding-right: $spacing-4;
  
  @include tablet-up {
    padding-left: $spacing-6;
    padding-right: $spacing-6;
  }
  
  @include desktop-up {
    padding-left: $spacing-8;
    padding-right: $spacing-8;
  }
}

// ===== GRID SYSTEM =====
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -$spacing-3;
  margin-right: -$spacing-3;
  
  @include tablet-up {
    margin-left: -$spacing-4;
    margin-right: -$spacing-4;
  }
}

.col {
  flex: 1 0 0%;
  padding-left: $spacing-3;
  padding-right: $spacing-3;
  
  @include tablet-up {
    padding-left: $spacing-4;
    padding-right: $spacing-4;
  }
}

// ===== COLUMN SIZES =====
@for $i from 1 through 12 {
  .col-#{$i} {
    flex: 0 0 percentage($i / 12);
    max-width: percentage($i / 12);
    padding-left: $spacing-3;
    padding-right: $spacing-3;
    
    @include tablet-up {
      padding-left: $spacing-4;
      padding-right: $spacing-4;
    }
  }
}

// ===== RESPONSIVE COLUMNS =====
@include tablet-up {
  @for $i from 1 through 12 {
    .col-md-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  .col-md {
    flex: 1 0 0%;
  }
  
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
}

@include desktop-up {
  @for $i from 1 through 12 {
    .col-lg-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  .col-lg {
    flex: 1 0 0%;
  }
  
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
}

@include large-desktop-up {
  @for $i from 1 through 12 {
    .col-xl-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  .col-xl {
    flex: 1 0 0%;
  }
  
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
}

// ===== COLUMN OFFSETS =====
@for $i from 0 through 11 {
  .offset-#{$i} {
    margin-left: percentage($i / 12);
  }
}

@include tablet-up {
  @for $i from 0 through 11 {
    .offset-md-#{$i} {
      margin-left: percentage($i / 12);
    }
  }
}

@include desktop-up {
  @for $i from 0 through 11 {
    .offset-lg-#{$i} {
      margin-left: percentage($i / 12);
    }
  }
}

@include large-desktop-up {
  @for $i from 0 through 11 {
    .offset-xl-#{$i} {
      margin-left: percentage($i / 12);
    }
  }
}

// ===== COLUMN ORDERING =====
.order-first { order: -1; }
.order-last { order: 13; }

@for $i from 0 through 12 {
  .order-#{$i} {
    order: $i;
  }
}

@include tablet-up {
  .order-md-first { order: -1; }
  .order-md-last { order: 13; }
  
  @for $i from 0 through 12 {
    .order-md-#{$i} {
      order: $i;
    }
  }
}

@include desktop-up {
  .order-lg-first { order: -1; }
  .order-lg-last { order: 13; }
  
  @for $i from 0 through 12 {
    .order-lg-#{$i} {
      order: $i;
    }
  }
}

@include large-desktop-up {
  .order-xl-first { order: -1; }
  .order-xl-last { order: 13; }
  
  @for $i from 0 through 12 {
    .order-xl-#{$i} {
      order: $i;
    }
  }
}

// ===== NO GUTTERS =====
.no-gutters {
  margin-left: 0;
  margin-right: 0;
  
  > .col,
  > [class*="col-"] {
    padding-left: 0;
    padding-right: 0;
  }
}

// ===== ALIGNMENT =====
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.justify-content-evenly { justify-content: space-evenly; }

.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }

.align-content-start { align-content: flex-start; }
.align-content-end { align-content: flex-end; }
.align-content-center { align-content: center; }
.align-content-between { align-content: space-between; }
.align-content-around { align-content: space-around; }
.align-content-stretch { align-content: stretch; }

.align-self-auto { align-self: auto; }
.align-self-start { align-self: flex-start; }
.align-self-end { align-self: flex-end; }
.align-self-center { align-self: center; }
.align-self-baseline { align-self: baseline; }
.align-self-stretch { align-self: stretch; }

// ===== RESPONSIVE ALIGNMENT =====
@include tablet-up {
  .justify-content-md-start { justify-content: flex-start; }
  .justify-content-md-end { justify-content: flex-end; }
  .justify-content-md-center { justify-content: center; }
  .justify-content-md-between { justify-content: space-between; }
  .justify-content-md-around { justify-content: space-around; }
  .justify-content-md-evenly { justify-content: space-evenly; }
  
  .align-items-md-start { align-items: flex-start; }
  .align-items-md-end { align-items: flex-end; }
  .align-items-md-center { align-items: center; }
  .align-items-md-baseline { align-items: baseline; }
  .align-items-md-stretch { align-items: stretch; }
}

@include desktop-up {
  .justify-content-lg-start { justify-content: flex-start; }
  .justify-content-lg-end { justify-content: flex-end; }
  .justify-content-lg-center { justify-content: center; }
  .justify-content-lg-between { justify-content: space-between; }
  .justify-content-lg-around { justify-content: space-around; }
  .justify-content-lg-evenly { justify-content: space-evenly; }
  
  .align-items-lg-start { align-items: flex-start; }
  .align-items-lg-end { align-items: flex-end; }
  .align-items-lg-center { align-items: center; }
  .align-items-lg-baseline { align-items: baseline; }
  .align-items-lg-stretch { align-items: stretch; }
}

// ===== CSS GRID UTILITIES =====
.grid-container {
  display: grid;
  gap: $spacing-6;
  
  @include mobile-only {
    gap: $spacing-4;
  }
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  
  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  
  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

// ===== MASONRY LAYOUT =====
.masonry {
  column-count: 3;
  column-gap: $spacing-6;
  
  @include tablet-up {
    column-count: 2;
  }
  
  @include mobile-only {
    column-count: 1;
    column-gap: $spacing-4;
  }
  
  > * {
    break-inside: avoid;
    margin-bottom: $spacing-6;
    
    @include mobile-only {
      margin-bottom: $spacing-4;
    }
  }
}
