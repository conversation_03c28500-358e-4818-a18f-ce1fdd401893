import { useCallback, useEffect, useState } from '@lynx-js/react'

import './styles/main.scss'

export function App(props: {
  onMounted?: () => void
}) {
  const [currentUser, setCurrentUser] = useState<string | null>(null)
  const [theme, setTheme] = useState<'light' | 'dark'>('light')

  useEffect(() => {
    console.info('Cambodian Loan Management App - Starting...')
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', theme)
    document.body.className = `theme-${theme}`
    props.onMounted?.()
  }, [theme])

  const handleLogin = useCallback(() => {
    setCurrentUser('admin')
  }, [])

  const handleLogout = useCallback(() => {
    setCurrentUser(null)
  }, [])

  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light')
  }, [])

  if (!currentUser) {
    return (
      <view className="dashboard flex flex-col justify-center items-center min-h-screen p-6">
        {/* Floating decorations */}
        <view className="floating-decoration decoration-1" />
        <view className="floating-decoration decoration-2" />
        <view className="floating-decoration decoration-3" />
        <view className="floating-decoration decoration-4" />

        {/* Login Card */}
        <view className="card-glass p-8 text-center animate-fade-in-scale">
          {/* Logo and Title */}
          <view className="flex flex-col items-center mb-8">
            <view className="w-20 h-20 bg-gradient-temple rounded-full flex justify-center items-center mb-6 animate-floating">
              <text className="text-white text-4xl font-bold text-shadow-strong">
                ₹
              </text>
            </view>

            <text className="text-display-1 text-gradient-temple mb-2 khmer-text">
              ប្រព័ន្ធគ្រប់គ្រងកម្ចី
            </text>

            <text className="text-lg text-secondary khmer-text">
              Cambodian Loan Management System
            </text>
          </view>

          {/* Login Button */}
          <view
            className="btn btn-temple btn-lg mb-4 hover-lift animate-pulse"
            bindtap={handleLogin}
          >
            <text className="khmer-text font-semibold">
              ចូលប្រើប្រាស់ / Login
            </text>
          </view>

          <text className="text-xs text-muted">
            Demo: Tap to login as admin
          </text>
        </view>

        {/* Theme Toggle */}
        <view
          className="btn-fab-secondary mt-8"
          bindtap={toggleTheme}
        >
          <text>{theme === 'light' ? '🌙' : '☀️'}</text>
        </view>
      </view>
    )
  }

  return (
    <view className="dashboard min-h-screen">
      {/* Floating decorations */}
      <view className="floating-decoration decoration-1" />
      <view className="floating-decoration decoration-2" />
      <view className="floating-decoration decoration-3" />
      <view className="floating-decoration decoration-4" />

      {/* Header */}
      <view className="navbar bg-glass backdrop-blur-lg border-b border-primary/20 px-6 py-4">
        <view className="flex justify-between items-center">
          <view className="animate-slide-in-left">
            <text className="text-heading-1 text-gradient-temple khmer-text">
              ប្រព័ន្ធគ្រប់គ្រងកម្ចី
            </text>
            <text className="text-sm text-secondary khmer-text opacity-90">
              សួស្តី Admin
            </text>
          </view>

          <view className="flex items-center gap-4">
            {/* Theme Toggle */}
            <view
              className="btn-glass btn-circle hover-scale"
              bindtap={toggleTheme}
            >
              <text>{theme === 'light' ? '🌙' : '☀️'}</text>
            </view>

            {/* Logout Button */}
            <view
              className="btn btn-outline-primary hover-lift"
              bindtap={handleLogout}
            >
              <text className="khmer-text">ចាកចេញ</text>
            </view>
          </view>
        </view>
      </view>

      {/* Dashboard Content */}
      <scroll-view className="flex-1">
        <view className="dashboard-container">
          {/* Dashboard Header */}
          <view className="dashboard-header animate-fade-in">
            <text className="dashboard-title">
              ផ្ទាំងគ្រប់គ្រង
            </text>
            <text className="dashboard-subtitle">
              Dashboard Overview
            </text>
            <text className="dashboard-date">
              {new Date().toLocaleDateString('km-KH')}
            </text>
          </view>

          {/* Stats Section */}
          <view className="dashboard-section">
            <text className="dashboard-section-title animate-slide-in-left">
              ទិដ្ឋភាពទូទៅ / Overview
            </text>

            <view className="stats-grid">
              <StatCard
                title="កម្ចីសកម្ម"
                subtitle="Active Loans"
                value="156"
                variant="temple-blue"
                icon="💰"
                delay="0.1s"
              />
              <StatCard
                title="ចំនួនកម្ចីសរុប"
                subtitle="Total Amount"
                value="$2,450,000"
                variant="khmer-gold"
                icon="💎"
                delay="0.2s"
              />
              <StatCard
                title="កម្ចីយឺតយ៉ាវ"
                subtitle="Overdue Loans"
                value="23"
                variant="lotus-pink"
                icon="⚠️"
                delay="0.3s"
              />
              <StatCard
                title="អតិថិជនថ្មី"
                subtitle="New Clients"
                value="12"
                variant="sunset"
                icon="👥"
                delay="0.4s"
              />
            </view>
          </view>

          {/* Quick Actions */}
          <view className="dashboard-section">
            <text className="dashboard-section-title animate-slide-in-left">
              សកម្មភាពរហ័ស / Quick Actions
            </text>

            <view className="quick-actions-grid">
              <ActionButton
                title="បន្ថែមអតិថិជន"
                subtitle="Add Client"
                icon="👤"
                delay="0.1s"
              />
              <ActionButton
                title="បង្កើតកម្ចី"
                subtitle="New Loan"
                icon="💰"
                delay="0.2s"
              />
              <ActionButton
                title="កត់ត្រាការទូទាត់"
                subtitle="Record Payment"
                icon="💳"
                delay="0.3s"
              />
              <ActionButton
                title="មើលរបាយការណ៍"
                subtitle="View Reports"
                icon="📊"
                delay="0.4s"
              />
            </view>
          </view>

          {/* Recent Activity */}
          <view className="dashboard-section">
            <text className="dashboard-section-title animate-slide-in-left">
              សកម្មភាពថ្មីៗ / Recent Activity
            </text>

            <view className="activity-feed animate-fade-in">
              <ActivityItem
                title="កម្ចីថ្មីត្រូវបានអនុម័ត"
                subtitle="New loan approved - សុខ សុភា - $5,000"
                time="2h ago"
                type="success"
              />
              <ActivityItem
                title="ការទូទាត់ត្រូវបានកត់ត្រា"
                subtitle="Payment recorded - ចាន់ ដារា - $250"
                time="4h ago"
                type="primary"
              />
              <ActivityItem
                title="អតិថិជនថ្មីបានចុះឈ្មោះ"
                subtitle="New client registered - លី មុនី"
                time="6h ago"
                type="warning"
              />
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  )
}

function StatCard({ title, subtitle, value, variant, icon, delay }: {
  title: string
  subtitle: string
  value: string
  variant: 'temple-blue' | 'khmer-gold' | 'lotus-pink' | 'sunset'
  icon: string
  delay: string
}) {
  return (
    <view
      className={`stat-card-enhanced stat-card-${variant} animate-stagger`}
      style={{ animationDelay: delay }}
    >
      <view className={`stat-icon stat-icon-${variant} animate-floating`}>
        <text>{icon}</text>
      </view>

      <text className="stat-value animate-fade-in">
        {value}
      </text>

      <text className="stat-label khmer-text mb-2">
        {title}
      </text>

      <text className="text-xs text-muted">
        {subtitle}
      </text>

      <view className="stat-change stat-change-positive mt-2">
        <text className="text-xs">+12% from last month</text>
      </view>
    </view>
  )
}

function ActionButton({ title, subtitle, icon, delay }: {
  title: string
  subtitle: string
  icon: string
  delay: string
}) {
  return (
    <view
      className="quick-action-card animate-stagger"
      style={{ animationDelay: delay }}
    >
      <view className="quick-action-icon">
        <text>{icon}</text>
      </view>

      <text className="quick-action-title khmer-text mb-1">
        {title}
      </text>

      <text className="text-xs text-muted text-center">
        {subtitle}
      </text>
    </view>
  )
}

function ActivityItem({ title, subtitle, time, type }: {
  title: string
  subtitle: string
  time: string
  type: 'primary' | 'success' | 'warning' | 'danger'
}) {
  return (
    <view className="activity-item-enhanced">
      <view className={`activity-indicator-enhanced activity-indicator-${type}`} />

      <view className="activity-content-enhanced">
        <text className="activity-title-enhanced khmer-text">
          {title}
        </text>
        <text className="activity-subtitle-enhanced khmer-text">
          {subtitle}
        </text>
        <text className="activity-time-enhanced">
          {time}
        </text>
      </view>
    </view>
  )
}
