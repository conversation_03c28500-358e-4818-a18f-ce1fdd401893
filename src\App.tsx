import { useEffect } from '@lynx-js/react'
import { ThemeProvider } from './contexts/ThemeContext'
import { LoanManagementApp } from './components/LoanManagementApp'
import './styles/main.scss'

export function App(props: {
  onMounted?: () => void
}) {
  useEffect(() => {
    console.info('Cambodian Loan Management App - Starting...')
    props.onMounted?.()
  }, [])

  return (
    <ThemeProvider defaultTheme="system" enableTransitions={true}>
      <LoanManagementApp />
    </ThemeProvider>
  )
}
