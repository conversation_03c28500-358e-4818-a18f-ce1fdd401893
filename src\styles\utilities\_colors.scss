// Color Utilities

// ===== BACKGROUND COLORS =====
.bg-transparent { background-color: transparent; }
.bg-current { background-color: currentColor; }

.bg-white { background-color: $white; }
.bg-black { background-color: $black; }

.bg-gray-50 { background-color: $gray-50; }
.bg-gray-100 { background-color: $gray-100; }
.bg-gray-200 { background-color: $gray-200; }
.bg-gray-300 { background-color: $gray-300; }
.bg-gray-400 { background-color: $gray-400; }
.bg-gray-500 { background-color: $gray-500; }
.bg-gray-600 { background-color: $gray-600; }
.bg-gray-700 { background-color: $gray-700; }
.bg-gray-800 { background-color: $gray-800; }
.bg-gray-900 { background-color: $gray-900; }

.bg-primary { background-color: $primary; }
.bg-secondary { background-color: $secondary; }
.bg-accent { background-color: $accent; }
.bg-success { background-color: $success; }
.bg-warning { background-color: $warning; }
.bg-danger { background-color: $danger; }
.bg-info { background-color: $info; }

.bg-khmer-gold-50 { background-color: $khmer-gold-50; }
.bg-khmer-gold-100 { background-color: $khmer-gold-100; }
.bg-khmer-gold-200 { background-color: $khmer-gold-200; }
.bg-khmer-gold-300 { background-color: $khmer-gold-300; }
.bg-khmer-gold-400 { background-color: $khmer-gold-400; }
.bg-khmer-gold-500 { background-color: $khmer-gold-500; }
.bg-khmer-gold-600 { background-color: $khmer-gold-600; }
.bg-khmer-gold-700 { background-color: $khmer-gold-700; }
.bg-khmer-gold-800 { background-color: $khmer-gold-800; }
.bg-khmer-gold-900 { background-color: $khmer-gold-900; }

.bg-temple-blue-50 { background-color: $temple-blue-50; }
.bg-temple-blue-100 { background-color: $temple-blue-100; }
.bg-temple-blue-200 { background-color: $temple-blue-200; }
.bg-temple-blue-300 { background-color: $temple-blue-300; }
.bg-temple-blue-400 { background-color: $temple-blue-400; }
.bg-temple-blue-500 { background-color: $temple-blue-500; }
.bg-temple-blue-600 { background-color: $temple-blue-600; }
.bg-temple-blue-700 { background-color: $temple-blue-700; }
.bg-temple-blue-800 { background-color: $temple-blue-800; }
.bg-temple-blue-900 { background-color: $temple-blue-900; }

.bg-lotus-pink-50 { background-color: $lotus-pink-50; }
.bg-lotus-pink-100 { background-color: $lotus-pink-100; }
.bg-lotus-pink-200 { background-color: $lotus-pink-200; }
.bg-lotus-pink-300 { background-color: $lotus-pink-300; }
.bg-lotus-pink-400 { background-color: $lotus-pink-400; }
.bg-lotus-pink-500 { background-color: $lotus-pink-500; }
.bg-lotus-pink-600 { background-color: $lotus-pink-600; }
.bg-lotus-pink-700 { background-color: $lotus-pink-700; }
.bg-lotus-pink-800 { background-color: $lotus-pink-800; }
.bg-lotus-pink-900 { background-color: $lotus-pink-900; }

// ===== GRADIENT BACKGROUNDS =====
.bg-gradient-khmer-gold { @include gradient-khmer-gold; }
.bg-gradient-temple-blue { @include gradient-temple-blue; }
.bg-gradient-lotus-pink { @include gradient-lotus-pink; }
.bg-gradient-sunset { @include gradient-sunset; }
.bg-gradient-temple { @include gradient-temple; }

// ===== GLASS BACKGROUNDS =====
.bg-glass { @include glass-morphism; }
.bg-glass-dark { @include glass-morphism-dark; }

// ===== BORDER COLORS =====
.border-transparent { border-color: transparent; }
.border-current { border-color: currentColor; }

.border-white { border-color: $white; }
.border-black { border-color: $black; }

.border-gray-50 { border-color: $gray-50; }
.border-gray-100 { border-color: $gray-100; }
.border-gray-200 { border-color: $gray-200; }
.border-gray-300 { border-color: $gray-300; }
.border-gray-400 { border-color: $gray-400; }
.border-gray-500 { border-color: $gray-500; }
.border-gray-600 { border-color: $gray-600; }
.border-gray-700 { border-color: $gray-700; }
.border-gray-800 { border-color: $gray-800; }
.border-gray-900 { border-color: $gray-900; }

.border-primary { border-color: $primary; }
.border-secondary { border-color: $secondary; }
.border-accent { border-color: $accent; }
.border-success { border-color: $success; }
.border-warning { border-color: $warning; }
.border-danger { border-color: $danger; }
.border-info { border-color: $info; }

.border-khmer-gold-200 { border-color: $khmer-gold-200; }
.border-khmer-gold-300 { border-color: $khmer-gold-300; }
.border-khmer-gold-400 { border-color: $khmer-gold-400; }
.border-khmer-gold-500 { border-color: $khmer-gold-500; }
.border-khmer-gold-600 { border-color: $khmer-gold-600; }

.border-temple-blue-200 { border-color: $temple-blue-200; }
.border-temple-blue-300 { border-color: $temple-blue-300; }
.border-temple-blue-400 { border-color: $temple-blue-400; }
.border-temple-blue-500 { border-color: $temple-blue-500; }
.border-temple-blue-600 { border-color: $temple-blue-600; }

.border-lotus-pink-200 { border-color: $lotus-pink-200; }
.border-lotus-pink-300 { border-color: $lotus-pink-300; }
.border-lotus-pink-400 { border-color: $lotus-pink-400; }
.border-lotus-pink-500 { border-color: $lotus-pink-500; }
.border-lotus-pink-600 { border-color: $lotus-pink-600; }

// ===== BORDER WIDTH =====
.border-0 { border-width: 0; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-8 { border-width: 8px; }

.border-t-0 { border-top-width: 0; }
.border-t { border-top-width: 1px; }
.border-t-2 { border-top-width: 2px; }
.border-t-4 { border-top-width: 4px; }
.border-t-8 { border-top-width: 8px; }

.border-r-0 { border-right-width: 0; }
.border-r { border-right-width: 1px; }
.border-r-2 { border-right-width: 2px; }
.border-r-4 { border-right-width: 4px; }
.border-r-8 { border-right-width: 8px; }

.border-b-0 { border-bottom-width: 0; }
.border-b { border-bottom-width: 1px; }
.border-b-2 { border-bottom-width: 2px; }
.border-b-4 { border-bottom-width: 4px; }
.border-b-8 { border-bottom-width: 8px; }

.border-l-0 { border-left-width: 0; }
.border-l { border-left-width: 1px; }
.border-l-2 { border-left-width: 2px; }
.border-l-4 { border-left-width: 4px; }
.border-l-8 { border-left-width: 8px; }

// ===== BORDER RADIUS =====
.rounded-none { border-radius: $border-radius-none; }
.rounded-sm { border-radius: $border-radius-sm; }
.rounded { border-radius: $border-radius-base; }
.rounded-md { border-radius: $border-radius-md; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-xl { border-radius: $border-radius-xl; }
.rounded-full { border-radius: $border-radius-full; }

.rounded-t-none { border-top-left-radius: $border-radius-none; border-top-right-radius: $border-radius-none; }
.rounded-t-sm { border-top-left-radius: $border-radius-sm; border-top-right-radius: $border-radius-sm; }
.rounded-t { border-top-left-radius: $border-radius-base; border-top-right-radius: $border-radius-base; }
.rounded-t-md { border-top-left-radius: $border-radius-md; border-top-right-radius: $border-radius-md; }
.rounded-t-lg { border-top-left-radius: $border-radius-lg; border-top-right-radius: $border-radius-lg; }
.rounded-t-xl { border-top-left-radius: $border-radius-xl; border-top-right-radius: $border-radius-xl; }

.rounded-r-none { border-top-right-radius: $border-radius-none; border-bottom-right-radius: $border-radius-none; }
.rounded-r-sm { border-top-right-radius: $border-radius-sm; border-bottom-right-radius: $border-radius-sm; }
.rounded-r { border-top-right-radius: $border-radius-base; border-bottom-right-radius: $border-radius-base; }
.rounded-r-md { border-top-right-radius: $border-radius-md; border-bottom-right-radius: $border-radius-md; }
.rounded-r-lg { border-top-right-radius: $border-radius-lg; border-bottom-right-radius: $border-radius-lg; }
.rounded-r-xl { border-top-right-radius: $border-radius-xl; border-bottom-right-radius: $border-radius-xl; }

.rounded-b-none { border-bottom-right-radius: $border-radius-none; border-bottom-left-radius: $border-radius-none; }
.rounded-b-sm { border-bottom-right-radius: $border-radius-sm; border-bottom-left-radius: $border-radius-sm; }
.rounded-b { border-bottom-right-radius: $border-radius-base; border-bottom-left-radius: $border-radius-base; }
.rounded-b-md { border-bottom-right-radius: $border-radius-md; border-bottom-left-radius: $border-radius-md; }
.rounded-b-lg { border-bottom-right-radius: $border-radius-lg; border-bottom-left-radius: $border-radius-lg; }
.rounded-b-xl { border-bottom-right-radius: $border-radius-xl; border-bottom-left-radius: $border-radius-xl; }

.rounded-l-none { border-top-left-radius: $border-radius-none; border-bottom-left-radius: $border-radius-none; }
.rounded-l-sm { border-top-left-radius: $border-radius-sm; border-bottom-left-radius: $border-radius-sm; }
.rounded-l { border-top-left-radius: $border-radius-base; border-bottom-left-radius: $border-radius-base; }
.rounded-l-md { border-top-left-radius: $border-radius-md; border-bottom-left-radius: $border-radius-md; }
.rounded-l-lg { border-top-left-radius: $border-radius-lg; border-bottom-left-radius: $border-radius-lg; }
.rounded-l-xl { border-top-left-radius: $border-radius-xl; border-bottom-left-radius: $border-radius-xl; }

// ===== BOX SHADOW =====
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: $shadow-sm; }
.shadow { box-shadow: $shadow-base; }
.shadow-md { box-shadow: $shadow-md; }
.shadow-lg { box-shadow: $shadow-lg; }
.shadow-xl { box-shadow: $shadow-xl; }
.shadow-2xl { box-shadow: $shadow-2xl; }

.shadow-khmer-gold { box-shadow: $shadow-khmer-gold; }
.shadow-temple-blue { box-shadow: $shadow-temple-blue; }
.shadow-lotus-pink { box-shadow: $shadow-lotus-pink; }

// ===== OPACITY =====
.opacity-0 { opacity: 0; }
.opacity-5 { opacity: 0.05; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-25 { opacity: 0.25; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-75 { opacity: 0.75; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-95 { opacity: 0.95; }
.opacity-100 { opacity: 1; }

// ===== BACKDROP FILTER =====
.backdrop-blur-none { backdrop-filter: none; }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur { backdrop-filter: blur(8px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }
.backdrop-blur-lg { backdrop-filter: blur(16px); }
.backdrop-blur-xl { backdrop-filter: blur(24px); }
.backdrop-blur-2xl { backdrop-filter: blur(40px); }
.backdrop-blur-3xl { backdrop-filter: blur(64px); }
