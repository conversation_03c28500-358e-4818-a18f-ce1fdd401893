// Display Utilities

// ===== DISPLAY =====
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.inline-grid { display: inline-grid; }
.table { display: table; }
.table-cell { display: table-cell; }
.table-row { display: table-row; }
.hidden { display: none; }

// ===== FLEXBOX UTILITIES =====

// Flex Direction
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

// Flex Wrap
.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

// Flex
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

// Flex Grow
.flex-grow-0 { flex-grow: 0; }
.flex-grow { flex-grow: 1; }

// Flex Shrink
.flex-shrink-0 { flex-shrink: 0; }
.flex-shrink { flex-shrink: 1; }

// Justify Content
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

// Align Items
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

// Align Content
.content-start { align-content: flex-start; }
.content-end { align-content: flex-end; }
.content-center { align-content: center; }
.content-between { align-content: space-between; }
.content-around { align-content: space-around; }
.content-evenly { align-content: space-evenly; }

// Align Self
.self-auto { align-self: auto; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }
.self-center { align-self: center; }
.self-stretch { align-self: stretch; }
.self-baseline { align-self: baseline; }

// ===== GRID UTILITIES =====

// Grid Template Columns
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
.grid-cols-none { grid-template-columns: none; }

// Grid Template Rows
.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }
.grid-rows-none { grid-template-rows: none; }

// Grid Column Span
.col-auto { grid-column: auto; }
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-5 { grid-column: span 5 / span 5; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-full { grid-column: 1 / -1; }

// Grid Row Span
.row-auto { grid-row: auto; }
.row-span-1 { grid-row: span 1 / span 1; }
.row-span-2 { grid-row: span 2 / span 2; }
.row-span-3 { grid-row: span 3 / span 3; }
.row-span-4 { grid-row: span 4 / span 4; }
.row-span-5 { grid-row: span 5 / span 5; }
.row-span-6 { grid-row: span 6 / span 6; }
.row-span-full { grid-row: 1 / -1; }

// Grid Auto Flow
.grid-flow-row { grid-auto-flow: row; }
.grid-flow-col { grid-auto-flow: column; }
.grid-flow-row-dense { grid-auto-flow: row dense; }
.grid-flow-col-dense { grid-auto-flow: column dense; }

// ===== POSITION =====
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

// ===== TOP / RIGHT / BOTTOM / LEFT =====
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-auto { top: auto; right: auto; bottom: auto; left: auto; }

.top-0 { top: 0; }
.top-auto { top: auto; }
.right-0 { right: 0; }
.right-auto { right: auto; }
.bottom-0 { bottom: 0; }
.bottom-auto { bottom: auto; }
.left-0 { left: 0; }
.left-auto { left: auto; }

// ===== VISIBILITY =====
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// ===== Z-INDEX =====
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

// ===== OVERFLOW =====
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-visible { overflow-y: visible; }
.overflow-y-scroll { overflow-y: scroll; }

// ===== OBJECT FIT =====
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

// ===== OBJECT POSITION =====
.object-bottom { object-position: bottom; }
.object-center { object-position: center; }
.object-left { object-position: left; }
.object-left-bottom { object-position: left bottom; }
.object-left-top { object-position: left top; }
.object-right { object-position: right; }
.object-right-bottom { object-position: right bottom; }
.object-right-top { object-position: right top; }
.object-top { object-position: top; }

// ===== RESPONSIVE DISPLAY UTILITIES =====
@include mobile-only {
  .block-mobile { display: block; }
  .inline-block-mobile { display: inline-block; }
  .inline-mobile { display: inline; }
  .flex-mobile { display: flex; }
  .inline-flex-mobile { display: inline-flex; }
  .grid-mobile { display: grid; }
  .hidden-mobile { display: none; }
  
  .flex-col-mobile { flex-direction: column; }
  .flex-row-mobile { flex-direction: row; }
  
  .justify-center-mobile { justify-content: center; }
  .justify-start-mobile { justify-content: flex-start; }
  .justify-end-mobile { justify-content: flex-end; }
  .justify-between-mobile { justify-content: space-between; }
  
  .items-center-mobile { align-items: center; }
  .items-start-mobile { align-items: flex-start; }
  .items-end-mobile { align-items: flex-end; }
  
  .grid-cols-1-mobile { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-cols-2-mobile { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@include tablet-up {
  .block-tablet { display: block; }
  .inline-block-tablet { display: inline-block; }
  .inline-tablet { display: inline; }
  .flex-tablet { display: flex; }
  .inline-flex-tablet { display: inline-flex; }
  .grid-tablet { display: grid; }
  .hidden-tablet { display: none; }
  
  .flex-col-tablet { flex-direction: column; }
  .flex-row-tablet { flex-direction: row; }
  
  .justify-center-tablet { justify-content: center; }
  .justify-start-tablet { justify-content: flex-start; }
  .justify-end-tablet { justify-content: flex-end; }
  .justify-between-tablet { justify-content: space-between; }
  
  .items-center-tablet { align-items: center; }
  .items-start-tablet { align-items: flex-start; }
  .items-end-tablet { align-items: flex-end; }
  
  .grid-cols-2-tablet { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-3-tablet { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-cols-4-tablet { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@include desktop-up {
  .block-desktop { display: block; }
  .inline-block-desktop { display: inline-block; }
  .inline-desktop { display: inline; }
  .flex-desktop { display: flex; }
  .inline-flex-desktop { display: inline-flex; }
  .grid-desktop { display: grid; }
  .hidden-desktop { display: none; }
  
  .flex-col-desktop { flex-direction: column; }
  .flex-row-desktop { flex-direction: row; }
  
  .justify-center-desktop { justify-content: center; }
  .justify-start-desktop { justify-content: flex-start; }
  .justify-end-desktop { justify-content: flex-end; }
  .justify-between-desktop { justify-content: space-between; }
  
  .items-center-desktop { align-items: center; }
  .items-start-desktop { align-items: flex-start; }
  .items-end-desktop { align-items: flex-end; }
  
  .grid-cols-3-desktop { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-cols-4-desktop { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-cols-5-desktop { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .grid-cols-6-desktop { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

// ===== MODERN RESPONSIVE UTILITIES =====
.hidden {
  display: none !important;
}

.block {
  display: block !important;
}

.inline-block {
  display: inline-block !important;
}

.flex {
  display: flex !important;
}

.inline-flex {
  display: inline-flex !important;
}

.grid {
  display: grid !important;
}

// Mobile-first responsive classes
@media (min-width: 640px) {
  .sm\\:hidden {
    display: none !important;
  }

  .sm\\:block {
    display: block !important;
  }

  .sm\\:flex {
    display: flex !important;
  }
}

@media (min-width: 768px) {
  .md\\:hidden {
    display: none !important;
  }

  .md\\:block {
    display: block !important;
  }

  .md\\:flex {
    display: flex !important;
  }
}

@media (min-width: 1024px) {
  .lg\\:hidden {
    display: none !important;
  }

  .lg\\:block {
    display: block !important;
  }

  .lg\\:flex {
    display: flex !important;
  }
}
