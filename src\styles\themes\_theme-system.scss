// Theme System for Khmer Loan Management App
// Provides theme switching functionality and system integration

// ===== THEME SYSTEM VARIABLES =====
:root {
  --theme-transition-duration: 0.3s;
  --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

// ===== THEME SWITCHING ANIMATIONS =====
* {
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

// Disable transitions during theme switching to prevent flash
.theme-switching * {
  transition: none !important;
}

// ===== THEME TOGGLE BUTTON =====
.theme-toggle {
  position: relative;
  width: 60px;
  height: 32px;
  background: var(--border-secondary);
  border-radius: 16px;
  border: none;
  cursor: pointer;
  transition: all var(--theme-transition-duration) var(--theme-transition-timing);
  overflow: hidden;
  
  &:hover {
    background: var(--border-primary);
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  // Toggle indicator
  &::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 28px;
    height: 28px;
    background: var(--bg-primary);
    border-radius: 50%;
    transition: all var(--theme-transition-duration) var(--theme-transition-timing);
    box-shadow: 0 2px 4px var(--shadow-color);
  }
  
  // Sun icon (light mode)
  &::after {
    content: '☀️';
    position: absolute;
    top: 50%;
    left: 8px;
    transform: translateY(-50%);
    font-size: 16px;
    transition: all var(--theme-transition-duration) var(--theme-transition-timing);
    opacity: 1;
  }
  
  // Dark mode state
  &.theme-toggle-dark {
    background: var(--color-primary);
    
    &::before {
      left: 30px;
    }
    
    &::after {
      content: '🌙';
      left: 36px;
      opacity: 1;
    }
  }
}

// ===== THEME-AWARE COMPONENTS =====
.theme-aware {
  // Automatically inherit theme colors
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

// ===== SYSTEM THEME DETECTION =====
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    // Apply dark theme variables when system prefers dark
    @import 'dark';
  }
}

@media (prefers-color-scheme: light) {
  :root:not([data-theme]) {
    // Apply light theme variables when system prefers light
    @import 'light';
  }
}

// ===== THEME PERSISTENCE =====
// These classes are applied by JavaScript based on localStorage
.theme-system {
  // Use system preference - using mixins instead of @extend to avoid media query issues
  @media (prefers-color-scheme: dark) {
    // Apply dark theme variables directly
    --bg-primary: var(--dark-bg-primary);
    --bg-secondary: var(--dark-bg-secondary);
    --text-primary: var(--dark-text-primary);
    --text-secondary: var(--dark-text-secondary);
    --color-primary: var(--dark-color-primary);
    --border-primary: var(--dark-border-primary);
  }

  @media (prefers-color-scheme: light) {
    // Apply light theme variables directly
    --bg-primary: var(--light-bg-primary);
    --bg-secondary: var(--light-bg-secondary);
    --text-primary: var(--light-text-primary);
    --text-secondary: var(--light-text-secondary);
    --color-primary: var(--light-color-primary);
    --border-primary: var(--light-border-primary);
  }
}

// ===== THEME INDICATOR =====
.theme-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  font-size: 12px;
  color: var(--text-secondary);
  pointer-events: none;
  opacity: 0;
  transform: translateY(-10px);
  transition: all var(--theme-transition-duration) var(--theme-transition-timing);
  
  &.theme-indicator-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-primary);
  }
}

// ===== THEME LOADING STATE =====
.theme-loading {
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-primary);
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.3s ease-out;
  }
  
  &.theme-loaded::before {
    opacity: 0;
    pointer-events: none;
  }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
@media (prefers-reduced-motion: reduce) {
  .theme-toggle,
  .theme-indicator,
  .theme-loading {
    transition: none;
  }
  
  * {
    transition: none !important;
  }
}

// ===== HIGH CONTRAST SUPPORT =====
@media (prefers-contrast: high) {
  .theme-toggle {
    border: 2px solid var(--text-primary);
    
    &::before {
      border: 1px solid var(--text-primary);
    }
  }
}

// ===== FORCED COLORS SUPPORT =====
@media (forced-colors: active) {
  .theme-toggle {
    forced-color-adjust: none;
    background: ButtonFace;
    border: 1px solid ButtonText;
    
    &::before {
      background: ButtonText;
    }
    
    &::after {
      color: ButtonText;
    }
  }
}

// ===== THEME UTILITIES =====
.theme-transition-fast {
  transition-duration: 0.15s !important;
}

.theme-transition-slow {
  transition-duration: 0.6s !important;
}

.theme-no-transition {
  transition: none !important;
}

// ===== THEME DEBUG MODE =====
.theme-debug {
  &::after {
    content: attr(data-theme);
    position: fixed;
    bottom: 20px;
    left: 20px;
    padding: 4px 8px;
    background: var(--color-danger);
    color: var(--text-inverse);
    font-size: 12px;
    border-radius: 4px;
    z-index: 9999;
    pointer-events: none;
  }
}
