import { useState, useCallback } from '@lynx-js/react'
import { Employee } from '../../types'
import { useTheme } from '../../contexts/ThemeContext'
import { ThemeToggle } from '../common/ThemeToggle'
import { Dashboard } from '../dashboard/Dashboard'
import { ClientManagement } from '../clients/ClientManagement'
import { LoanManagement } from '../loans/LoanManagement'
import { PaymentManagement } from '../payments/PaymentManagement'

interface MainNavigationProps {
  currentUser: Employee
  onLogout: () => void
}

type TabType = 'dashboard' | 'clients' | 'loans' | 'payments' | 'settings'

export function MainNavigation({ currentUser, onLogout }: MainNavigationProps) {
  const [activeTab, setActiveTab] = useState<TabType>('dashboard')
  const [language, setLanguage] = useState<'en' | 'km'>('en')
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { resolvedTheme } = useTheme()

  const handleTabChange = useCallback((tab: TabType) => {
    setActiveTab(tab)
    setIsMobileMenuOpen(false) // Close mobile menu when tab changes
  }, [])

  const toggleLanguage = useCallback(() => {
    setLanguage(prev => prev === 'en' ? 'km' : 'en')
  }, [])

  const toggleMobileMenu = useCallback(() => {
    setIsMobileMenuOpen(prev => !prev)
  }, [])

  const tabs = {
    en: {
      dashboard: 'Dashboard',
      clients: 'Clients',
      loans: 'Loans',
      payments: 'Payments',
      settings: 'Settings'
    },
    km: {
      dashboard: 'ផ្ទាំងគ្រប់គ្រង',
      clients: 'អតិថិជន',
      loans: 'កម្ចី',
      payments: 'ការទូទាត់',
      settings: 'ការកំណត់'
    }
  }

  const t = tabs[language]

  // Get icon for each tab
  const getTabIcon = (tab: TabType): string => {
    const icons = {
      dashboard: '📊',
      clients: '👥',
      loans: '💰',
      payments: '💳',
      settings: '⚙️'
    }
    return icons[tab]
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard currentUser={currentUser} language={language} />
      case 'clients':
        return <ClientManagement currentUser={currentUser} language={language} />
      case 'loans':
        return <LoanManagement currentUser={currentUser} language={language} />
      case 'payments':
        return <PaymentManagement currentUser={currentUser} language={language} />
      case 'settings':
        return <SettingsScreen currentUser={currentUser} language={language} onLogout={onLogout} />
      default:
        return <Dashboard currentUser={currentUser} language={language} />
    }
  }

  return (
    <view className="theme-aware min-h-screen">
      {/* Floating decorations */}
      <view className="floating-decoration decoration-1" />
      <view className="floating-decoration decoration-2" />
      <view className="floating-decoration decoration-3" />
      <view className="floating-decoration decoration-4" />

      {/* Header */}
      <view className="navbar bg-glass backdrop-blur-lg border-b border-primary/20 px-6 py-4">
        <view className="flex justify-between items-center">
          {/* Mobile Menu Button */}
          <view
            className="btn-glass btn-circle hover-scale md:hidden"
            bindtap={toggleMobileMenu}
          >
            <text>{isMobileMenuOpen ? '✕' : '☰'}</text>
          </view>

          {/* Logo and Title */}
          <view className="animate-slide-in-left">
            <text className="text-heading-1 text-gradient-temple khmer-text">
              {language === 'km' ? 'ប្រព័ន្ធគ្រប់គ្រងកម្ចី' : 'Loan Management'}
            </text>
            <text className="text-sm text-secondary khmer-text opacity-90 hidden sm:block">
              {language === 'km' ? `សួស្តី ${currentUser.fullNameKhmer || currentUser.fullName}` : `Hello, ${currentUser.fullName}`}
            </text>
          </view>

          {/* Header Actions */}
          <view className="flex items-center gap-4">
            {/* Language Toggle */}
            <view
              className="btn-glass btn-circle hover-scale"
              bindtap={toggleLanguage}
            >
              <text className="text-sm font-semibold">
                {language === 'km' ? 'EN' : 'ខ្មែរ'}
              </text>
            </view>

            {/* Theme Toggle */}
            <ThemeToggle
              size="medium"
              showIndicator={true}
              language={language}
            />

            {/* User Avatar */}
            <view className="avatar avatar-circle bg-gradient-temple">
              <text className="text-white font-bold">
                {currentUser.fullName.charAt(0).toUpperCase()}
              </text>
            </view>

            {/* Logout Button - Hidden on mobile */}
            <view
              className="btn btn-outline-primary hover-lift hidden md:flex"
              bindtap={onLogout}
            >
              <text className="khmer-text">
                {language === 'km' ? 'ចាកចេញ' : 'Logout'}
              </text>
            </view>
          </view>
        </view>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <view className="mobile-menu-overlay md:hidden">
            <view className="mobile-menu-content">
              {/* Mobile User Info */}
              <view className="mobile-user-info">
                <view className="avatar avatar-lg bg-gradient-temple mb-3">
                  <text className="text-white text-xl font-bold">
                    {currentUser.fullName.charAt(0).toUpperCase()}
                  </text>
                </view>
                <text className="text-lg font-semibold text-primary khmer-text">
                  {language === 'km' ? currentUser.fullNameKhmer || currentUser.fullName : currentUser.fullName}
                </text>
                <text className="text-sm text-secondary">
                  {currentUser.role}
                </text>
              </view>

              {/* Mobile Navigation */}
              <view className="mobile-nav-links">
                {(['dashboard', 'clients', 'loans', 'payments', 'settings'] as TabType[]).map((tab) => (
                  <view
                    key={tab}
                    className={`mobile-nav-link ${activeTab === tab ? 'mobile-nav-link-active' : ''}`}
                    bindtap={() => handleTabChange(tab)}
                  >
                    <text className="khmer-text">{t[tab]}</text>
                  </view>
                ))}
              </view>

              {/* Mobile Logout */}
              <view
                className="btn btn-outline-danger w-full mt-6"
                bindtap={onLogout}
              >
                <text className="khmer-text">
                  {language === 'km' ? 'ចាកចេញ' : 'Logout'}
                </text>
              </view>
            </view>
          </view>
        )}
      </view>

      {/* Main Content Area */}
      <view className="main-content">
        <view className="content-container">
          {renderContent()}
        </view>
      </view>

      {/* Bottom Navigation - Mobile Only */}
      <view className="bottom-nav md:hidden">
        <view className="bottom-nav-container">
          {(['dashboard', 'clients', 'loans', 'payments', 'settings'] as TabType[]).map((tab, index) => (
            <view
              key={tab}
              className={`bottom-nav-item ${activeTab === tab ? 'bottom-nav-item-active' : ''}`}
              bindtap={() => handleTabChange(tab)}
            >
              <view className={`bottom-nav-icon ${activeTab === tab ? 'bottom-nav-icon-active' : ''}`}>
                <text>{getTabIcon(tab)}</text>
              </view>
              <text className={`bottom-nav-label khmer-text ${activeTab === tab ? 'bottom-nav-label-active' : ''}`}>
                {t[tab]}
              </text>
              {activeTab === tab && (
                <view className="bottom-nav-indicator" />
              )}
            </view>
          ))}
        </view>
      </view>

      {/* Desktop Sidebar Navigation */}
      <view className="sidebar hidden md:flex">
        <view className="sidebar-content">
          <view className="sidebar-nav">
            {(['dashboard', 'clients', 'loans', 'payments', 'settings'] as TabType[]).map((tab) => (
              <view
                key={tab}
                className={`sidebar-nav-item ${activeTab === tab ? 'sidebar-nav-item-active' : ''}`}
                bindtap={() => handleTabChange(tab)}
              >
                <view className="sidebar-nav-icon">
                  <text>{getTabIcon(tab)}</text>
                </view>
                <text className="sidebar-nav-label khmer-text">
                  {t[tab]}
                </text>
              </view>
            ))}
          </view>
        </view>
      </view>
    </view>
  )
}

function SettingsScreen({ currentUser, language, onLogout }: {
  currentUser: Employee
  language: 'en' | 'km'
  onLogout: () => void
}) {
  const { resolvedTheme } = useTheme()

  const texts = {
    en: {
      title: 'Settings',
      profile: 'Profile Information',
      theme: 'Theme Settings',
      logout: 'Logout',
      confirmLogout: 'Are you sure you want to logout?'
    },
    km: {
      title: 'ការកំណត់',
      profile: 'ព័ត៌មានប្រវត្តិរូប',
      theme: 'ការកំណត់ធីម',
      logout: 'ចាកចេញ',
      confirmLogout: 'តើអ្នកពិតជាចង់ចាកចេញមែនទេ?'
    }
  }

  const t = texts[language]

  return (
    <view className="flex-1 p-6">
      <text className="text-display-1 text-primary khmer-text mb-8">
        {t.title}
      </text>

      {/* Profile Section */}
      <view className="card-glass p-6 mb-6 animate-fade-in">
        <text className="text-heading-2 text-primary khmer-text mb-4">
          {t.profile}
        </text>

        <view className="space-y-3">
          <view className="flex items-center gap-3">
            <view className="avatar avatar-lg bg-gradient-temple">
              <text className="text-white text-xl font-bold">
                {currentUser.fullName.charAt(0).toUpperCase()}
              </text>
            </view>
            <view>
              <text className="text-lg font-semibold text-primary khmer-text">
                {language === 'km' ? currentUser.fullNameKhmer || currentUser.fullName : currentUser.fullName}
              </text>
              <text className="text-sm text-secondary">
                {currentUser.role}
              </text>
            </view>
          </view>

          <view className="border-t border-primary/20 pt-4 mt-4">
            <text className="text-base text-secondary mb-2 khmer-text">
              {language === 'km' ? 'លេខទូរស័ព្ទ:' : 'Phone:'} {currentUser.phoneNumber}
            </text>
            <text className="text-base text-secondary khmer-text">
              {language === 'km' ? 'ភាសា:' : 'Language:'} {language === 'km' ? 'ខ្មែរ' : 'English'}
            </text>
          </view>
        </view>
      </view>

      {/* Theme Settings Section */}
      <view className="card-glass p-6 mb-6 animate-fade-in">
        <text className="text-heading-2 text-primary khmer-text mb-4">
          {t.theme}
        </text>

        <view className="flex items-center justify-between">
          <view>
            <text className="text-base font-medium text-primary khmer-text">
              {language === 'km' ? 'ប្តូរធីម' : 'Theme Toggle'}
            </text>
            <text className="text-sm text-secondary">
              {language === 'km' ? 'ប្តូររវាងពន្លឺ និងងងឹត' : 'Switch between light and dark themes'}
            </text>
          </view>

          <ThemeToggle
            size="large"
            showLabel={true}
            showIndicator={true}
            language={language}
          />
        </view>
      </view>

      {/* Logout Button */}
      <view
        className="btn btn-outline-danger btn-lg w-full hover-lift animate-fade-in"
        bindtap={onLogout}
      >
        <text className="khmer-text font-semibold">
          {t.logout}
        </text>
      </view>
    </view>
  )
}
