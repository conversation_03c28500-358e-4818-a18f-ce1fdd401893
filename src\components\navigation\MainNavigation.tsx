import { useState, useCallback } from '@lynx-js/react'
import { Employee } from '../../types'
import { Dashboard } from '../dashboard/Dashboard'
import { ClientManagement } from '../clients/ClientManagement'
import { LoanManagement } from '../loans/LoanManagement'
import { PaymentManagement } from '../payments/PaymentManagement'

interface MainNavigationProps {
  currentUser: Employee
  onLogout: () => void
}

type TabType = 'dashboard' | 'clients' | 'loans' | 'payments' | 'settings'

export function MainNavigation({ currentUser, onLogout }: MainNavigationProps) {
  const [activeTab, setActiveTab] = useState<TabType>('dashboard')
  const [language, setLanguage] = useState<'en' | 'km'>('en')

  const handleTabChange = useCallback((tab: TabType) => {
    setActiveTab(tab)
  }, [])

  const toggleLanguage = useCallback(() => {
    setLanguage(prev => prev === 'en' ? 'km' : 'en')
  }, [])

  const tabs = {
    en: {
      dashboard: 'Dashboard',
      clients: 'Clients',
      loans: 'Loans',
      payments: 'Payments',
      settings: 'Settings'
    },
    km: {
      dashboard: 'ផ្ទាំងគ្រប់គ្រង',
      clients: 'អតិថិជន',
      loans: 'កម្ចី',
      payments: 'ការទូទាត់',
      settings: 'ការកំណត់'
    }
  }

  const t = tabs[language]

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard currentUser={currentUser} language={language} />
      case 'clients':
        return <ClientManagement currentUser={currentUser} language={language} />
      case 'loans':
        return <LoanManagement currentUser={currentUser} language={language} />
      case 'payments':
        return <PaymentManagement currentUser={currentUser} language={language} />
      case 'settings':
        return <SettingsScreen currentUser={currentUser} language={language} onLogout={onLogout} />
      default:
        return <Dashboard currentUser={currentUser} language={language} />
    }
  }

  return (
    <view style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <view style={{
        backgroundColor: '#007bff',
        paddingTop: 40,
        paddingBottom: 10,
        paddingHorizontal: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <view>
          <text style={{ 
            color: 'white',
            fontSize: 18,
            fontWeight: 'bold'
          }}>
            {language === 'km' ? 'ប្រព័ន្ធគ្រប់គ្រងកម្ចី' : 'Loan Management'}
          </text>
          <text style={{ 
            color: 'rgba(255,255,255,0.8)',
            fontSize: 14
          }}>
            {language === 'km' ? `សួស្តី ${currentUser.fullNameKhmer || currentUser.fullName}` : `Hello, ${currentUser.fullName}`}
          </text>
        </view>
        
        <view style={{ flexDirection: 'row', alignItems: 'center' }}>
          <text 
            style={{ 
              color: 'white',
              fontSize: 14,
              marginRight: 15
            }}
            bindtap={toggleLanguage}
          >
            {language === 'km' ? 'EN' : 'ខ្មែរ'}
          </text>
          
          <view style={{
            width: 36,
            height: 36,
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: 18,
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <text style={{ 
              color: 'white',
              fontSize: 16,
              fontWeight: 'bold'
            }}>
              {currentUser.fullName.charAt(0).toUpperCase()}
            </text>
          </view>
        </view>
      </view>

      {/* Content */}
      <view style={{ flex: 1 }}>
        {renderContent()}
      </view>

      {/* Bottom Navigation */}
      <view style={{
        backgroundColor: 'white',
        flexDirection: 'row',
        paddingVertical: 10,
        paddingHorizontal: 5,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0'
      }}>
        {(['dashboard', 'clients', 'loans', 'payments', 'settings'] as TabType[]).map((tab) => (
          <view 
            key={tab}
            style={{
              flex: 1,
              alignItems: 'center',
              paddingVertical: 8
            }}
            bindtap={() => handleTabChange(tab)}
          >
            <view style={{
              width: 24,
              height: 24,
              backgroundColor: activeTab === tab ? '#007bff' : '#ccc',
              borderRadius: 12,
              marginBottom: 4
            }} />
            <text style={{
              fontSize: 12,
              color: activeTab === tab ? '#007bff' : '#666',
              fontWeight: activeTab === tab ? '600' : 'normal'
            }}>
              {t[tab]}
            </text>
          </view>
        ))}
      </view>
    </view>
  )
}

function SettingsScreen({ currentUser, language, onLogout }: {
  currentUser: Employee
  language: 'en' | 'km'
  onLogout: () => void
}) {
  const texts = {
    en: {
      title: 'Settings',
      profile: 'Profile Information',
      logout: 'Logout',
      confirmLogout: 'Are you sure you want to logout?'
    },
    km: {
      title: 'ការកំណត់',
      profile: 'ព័ត៌មានប្រវត្តិរូប',
      logout: 'ចាកចេញ',
      confirmLogout: 'តើអ្នកពិតជាចង់ចាកចេញមែនទេ?'
    }
  }

  const t = texts[language]

  return (
    <view style={{ flex: 1, padding: 20 }}>
      <text style={{ 
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 30,
        color: '#333'
      }}>
        {t.title}
      </text>

      {/* Profile Section */}
      <view style={{
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 20,
        marginBottom: 20
      }}>
        <text style={{ 
          fontSize: 18,
          fontWeight: '600',
          marginBottom: 15,
          color: '#333'
        }}>
          {t.profile}
        </text>
        
        <text style={{ fontSize: 16, marginBottom: 8, color: '#666' }}>
          {language === 'km' ? 'ឈ្មោះ:' : 'Name:'} {language === 'km' ? currentUser.fullNameKhmer || currentUser.fullName : currentUser.fullName}
        </text>
        <text style={{ fontSize: 16, marginBottom: 8, color: '#666' }}>
          {language === 'km' ? 'តួនាទី:' : 'Role:'} {currentUser.role}
        </text>
        <text style={{ fontSize: 16, marginBottom: 8, color: '#666' }}>
          {language === 'km' ? 'លេខទូរស័ព្ទ:' : 'Phone:'} {currentUser.phoneNumber}
        </text>
      </view>

      {/* Logout Button */}
      <view 
        style={{
          backgroundColor: '#dc3545',
          borderRadius: 8,
          height: 48,
          justifyContent: 'center',
          alignItems: 'center'
        }}
        bindtap={onLogout}
      >
        <text style={{ 
          color: 'white',
          fontSize: 16,
          fontWeight: '600'
        }}>
          {t.logout}
        </text>
      </view>
    </view>
  )
}
