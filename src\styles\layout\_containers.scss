// Container Layout Components

// ===== SECTION CONTAINERS =====
.section {
  padding: $spacing-16 0;
  
  @include mobile-only {
    padding: $spacing-10 0;
  }
}

.section-sm {
  padding: $spacing-10 0;
  
  @include mobile-only {
    padding: $spacing-6 0;
  }
}

.section-lg {
  padding: $spacing-20 0;
  
  @include mobile-only {
    padding: $spacing-12 0;
  }
}

// ===== CONTENT WRAPPERS =====
.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-6;
  
  @include mobile-only {
    padding: 0 $spacing-4;
  }
}

.content-narrow {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 $spacing-6;
  
  @include mobile-only {
    padding: 0 $spacing-4;
  }
}

.content-wide {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 $spacing-6;
  
  @include mobile-only {
    padding: 0 $spacing-4;
  }
}

// ===== GLASS CONTAINERS =====
.glass-container {
  @include glass-morphism;
  border-radius: $border-radius-xl;
  padding: $spacing-8;
  margin: $spacing-6 0;
  
  @include mobile-only {
    padding: $spacing-6;
    margin: $spacing-4 0;
  }
}

.glass-container-dark {
  @include glass-morphism-dark;
  border-radius: $border-radius-xl;
  padding: $spacing-8;
  margin: $spacing-6 0;
  color: $white;
  
  @include mobile-only {
    padding: $spacing-6;
    margin: $spacing-4 0;
  }
}

// ===== CARD CONTAINERS =====
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-6;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }
}

.card-container-sm {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-4;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-3;
  }
}

.card-container-lg {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: $spacing-8;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-6;
  }
}

// ===== SIDEBAR LAYOUTS =====
.sidebar-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: $spacing-8;
  min-height: 100vh;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: 0;
  }
}

.sidebar-layout-right {
  display: grid;
  grid-template-columns: 1fr 280px;
  gap: $spacing-8;
  min-height: 100vh;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: 0;
  }
}

.main-content {
  padding: $spacing-6;
  
  @include mobile-only {
    padding: $spacing-4;
  }
}

.sidebar {
  @include glass-morphism;
  padding: $spacing-6;
  height: fit-content;
  position: sticky;
  top: $spacing-6;
  
  @include mobile-only {
    position: static;
    margin-bottom: $spacing-4;
    padding: $spacing-4;
  }
}

// ===== HERO SECTIONS =====
.hero {
  position: relative;
  padding: $spacing-20 0;
  text-align: center;
  overflow: hidden;
  
  @include mobile-only {
    padding: $spacing-12 0;
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: $gradient-temple;
    opacity: 0.1;
    z-index: -1;
  }
}

.hero-glass {
  @extend .hero;
  @include glass-morphism;
  
  &::before {
    display: none;
  }
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 $spacing-6;
  
  @include mobile-only {
    padding: 0 $spacing-4;
  }
}

// ===== FEATURE SECTIONS =====
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-8;
  padding: $spacing-16 0;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-6;
    padding: $spacing-10 0;
  }
}

.feature-item {
  @include card-glass;
  text-align: center;
  @include hover-lift;
  
  .feature-icon {
    font-size: $font-size-4xl;
    margin-bottom: $spacing-4;
    color: $primary;
    @include floating-animation;
  }
  
  .feature-title {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-3;
    @include khmer-text;
  }
  
  .feature-description {
    color: $gray-600;
    @include khmer-text;
  }
}

// ===== STATS SECTIONS =====
.stats-section {
  padding: $spacing-16 0;
  background: $gradient-temple;
  color: $white;
  text-align: center;
  
  @include mobile-only {
    padding: $spacing-10 0;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-8;
  
  @include mobile-only {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-6;
  }
}

.stat-item {
  .stat-number {
    font-size: $font-size-4xl;
    font-weight: $font-weight-extrabold;
    margin-bottom: $spacing-2;
    @include text-shadow-strong;
    
    @include mobile-only {
      font-size: $font-size-3xl;
    }
  }
  
  .stat-label {
    font-size: $font-size-base;
    opacity: 0.9;
    @include khmer-text;
  }
}

// ===== TESTIMONIAL SECTIONS =====
.testimonials {
  padding: $spacing-16 0;
  background: $gray-50;
  
  @include mobile-only {
    padding: $spacing-10 0;
  }
}

.testimonial-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: $spacing-8;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: $spacing-6;
  }
}

.testimonial-item {
  @include card-glass;
  position: relative;
  
  &::before {
    content: '"';
    position: absolute;
    top: -$spacing-4;
    left: $spacing-6;
    font-size: $font-size-4xl;
    color: $primary;
    font-weight: $font-weight-bold;
  }
  
  .testimonial-text {
    margin-bottom: $spacing-6;
    font-style: italic;
    @include khmer-text;
  }
  
  .testimonial-author {
    display: flex;
    align-items: center;
    
    .author-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-right: $spacing-3;
      background: $gradient-khmer-gold;
    }
    
    .author-info {
      .author-name {
        font-weight: $font-weight-semibold;
        @include khmer-text;
      }
      
      .author-title {
        font-size: $font-size-sm;
        color: $gray-600;
        @include khmer-text;
      }
    }
  }
}

// ===== CTA SECTIONS =====
.cta-section {
  padding: $spacing-16 0;
  background: $gradient-sunset;
  color: $white;
  text-align: center;
  
  @include mobile-only {
    padding: $spacing-10 0;
  }
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
  
  .cta-title {
    font-size: $font-size-3xl;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-4;
    @include text-shadow-strong;
    @include khmer-text;
    
    @include mobile-only {
      font-size: $font-size-2xl;
    }
  }
  
  .cta-description {
    font-size: $font-size-lg;
    margin-bottom: $spacing-8;
    opacity: 0.9;
    @include khmer-text;
    
    @include mobile-only {
      font-size: $font-size-base;
      margin-bottom: $spacing-6;
    }
  }
}

// ===== RESPONSIVE CONTAINERS =====
@include mobile-only {
  .hide-mobile {
    display: none;
  }
  
  .show-mobile {
    display: block;
  }
}

@include tablet-up {
  .hide-tablet {
    display: none;
  }
  
  .show-tablet {
    display: block;
  }
}

@include desktop-up {
  .hide-desktop {
    display: none;
  }
  
  .show-desktop {
    display: block;
  }
}
