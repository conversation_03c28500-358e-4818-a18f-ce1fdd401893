import { useState, useEffect } from '@lynx-js/react'
import { Employee, Payment, LoanSchedule } from '../../types'
import { formatCurrency } from '../../utils/currency'
import { formatDate, isOverdue, getDaysUntilDue } from '../../utils/date'

interface PaymentManagementProps {
  currentUser: Employee
  language: 'en' | 'km'
}

// Mock payment data
const MOCK_PAYMENTS: Payment[] = [
  {
    id: '1',
    loanId: '1',
    amount: 444.24,
    paymentType: 'principal',
    paymentMethod: 'cash',
    receiptNumber: 'RCP001',
    paidAt: new Date('2024-06-15'),
    receivedBy: 'admin',
    isLate: false
  },
  {
    id: '2',
    loanId: '1',
    amount: 444.24,
    paymentType: 'principal',
    paymentMethod: 'bank_transfer',
    receiptNumber: 'RCP002',
    paidAt: new Date('2024-05-15'),
    receivedBy: 'officer',
    isLate: true,
    lateFee: 20
  }
]

// Mock loan schedule data
const MOCK_SCHEDULES: LoanSchedule[] = [
  {
    id: '1',
    loanId: '1',
    installmentNumber: 1,
    dueDate: new Date('2024-07-15'),
    principalAmount: 400,
    interestAmount: 44.24,
    totalAmount: 444.24,
    paidAmount: 0,
    remainingAmount: 444.24,
    status: 'pending'
  },
  {
    id: '2',
    loanId: '1',
    installmentNumber: 2,
    dueDate: new Date('2024-08-15'),
    principalAmount: 400,
    interestAmount: 44.24,
    totalAmount: 444.24,
    paidAmount: 0,
    remainingAmount: 444.24,
    status: 'pending'
  },
  {
    id: '3',
    loanId: '2',
    installmentNumber: 1,
    dueDate: new Date('2024-06-10'),
    principalAmount: 450,
    interestAmount: 34.97,
    totalAmount: 484.97,
    paidAmount: 0,
    remainingAmount: 484.97,
    status: 'overdue'
  }
]

export function PaymentManagement({ currentUser, language }: PaymentManagementProps) {
  const [payments, setPayments] = useState<Payment[]>([])
  const [schedules, setSchedules] = useState<LoanSchedule[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'payments' | 'schedule'>('schedule')

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setPayments(MOCK_PAYMENTS)
      setSchedules(MOCK_SCHEDULES)
      setIsLoading(false)
    }, 1000)
  }, [])

  const texts = {
    en: {
      title: 'Payment Management',
      payments: 'Payments',
      schedule: 'Payment Schedule',
      amount: 'Amount',
      date: 'Date',
      method: 'Method',
      receipt: 'Receipt',
      status: 'Status',
      dueDate: 'Due Date',
      installment: 'Installment',
      principal: 'Principal',
      interest: 'Interest',
      total: 'Total',
      paid: 'Paid',
      pending: 'Pending',
      overdue: 'Overdue',
      partial: 'Partial',
      cash: 'Cash',
      bank_transfer: 'Bank Transfer',
      mobile_money: 'Mobile Money',
      loading: 'Loading payments...',
      noPayments: 'No payments found',
      noSchedule: 'No payment schedule found',
      recordPayment: 'Record Payment',
      daysOverdue: 'days overdue',
      daysDue: 'days until due'
    },
    km: {
      title: 'គ្រប់គ្រងការទូទាត់',
      payments: 'ការទូទាត់',
      schedule: 'កាលវិភាគទូទាត់',
      amount: 'ចំនួនទឹកប្រាក់',
      date: 'កាលបរិច្ឆេទ',
      method: 'វិធីសាស្ត្រ',
      receipt: 'បង្កាន់ដៃ',
      status: 'ស្ថានភាព',
      dueDate: 'កាលបរិច្ឆេទកំណត់',
      installment: 'វគ្គ',
      principal: 'ដើម',
      interest: 'ការប្រាក់',
      total: 'សរុប',
      paid: 'បានទូទាត់',
      pending: 'កំពុងរង់ចាំ',
      overdue: 'យឺតយ៉ាវ',
      partial: 'ផ្នែក',
      cash: 'សាច់ប្រាក់',
      bank_transfer: 'ផ្ទេរធនាគារ',
      mobile_money: 'ប្រាក់ទូរស័ព្ទ',
      loading: 'កំពុងផ្ទុកការទូទាត់...',
      noPayments: 'រកមិនឃើញការទូទាត់',
      noSchedule: 'រកមិនឃើញកាលវិភាគទូទាត់',
      recordPayment: 'កត់ត្រាការទូទាត់',
      daysOverdue: 'ថ្ងៃយឺតយ៉ាវ',
      daysDue: 'ថ្ងៃរហូតដល់កំណត់'
    }
  }

  const t = texts[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return '#28a745'
      case 'pending': return '#ffc107'
      case 'overdue': return '#dc3545'
      case 'partial': return '#17a2b8'
      default: return '#6c757d'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid': return t.paid
      case 'pending': return t.pending
      case 'overdue': return t.overdue
      case 'partial': return t.partial
      default: return status
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return t.cash
      case 'bank_transfer': return t.bank_transfer
      case 'mobile_money': return t.mobile_money
      default: return method
    }
  }

  if (isLoading) {
    return (
      <view style={{ 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center' 
      }}>
        <text style={{ fontSize: 18, color: '#666' }}>
          {t.loading}
        </text>
      </view>
    )
  }

  return (
    <view style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <view style={{ 
        backgroundColor: 'white',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0'
      }}>
        <text style={{ 
          fontSize: 24,
          fontWeight: 'bold',
          color: '#333',
          marginBottom: 15
        }}>
          {t.title}
        </text>

        {/* Tab Navigation */}
        <view style={{ flexDirection: 'row', marginBottom: 10 }}>
          <view 
            style={{
              flex: 1,
              backgroundColor: activeTab === 'schedule' ? '#007bff' : '#f8f9fa',
              borderRadius: 8,
              paddingVertical: 12,
              alignItems: 'center',
              marginRight: 5
            }}
            bindtap={() => setActiveTab('schedule')}
          >
            <text style={{
              color: activeTab === 'schedule' ? 'white' : '#666',
              fontSize: 16,
              fontWeight: activeTab === 'schedule' ? '600' : 'normal'
            }}>
              {t.schedule}
            </text>
          </view>
          
          <view 
            style={{
              flex: 1,
              backgroundColor: activeTab === 'payments' ? '#007bff' : '#f8f9fa',
              borderRadius: 8,
              paddingVertical: 12,
              alignItems: 'center',
              marginLeft: 5
            }}
            bindtap={() => setActiveTab('payments')}
          >
            <text style={{
              color: activeTab === 'payments' ? 'white' : '#666',
              fontSize: 16,
              fontWeight: activeTab === 'payments' ? '600' : 'normal'
            }}>
              {t.payments}
            </text>
          </view>
        </view>

        {/* Stats */}
        <view style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 20, fontWeight: 'bold', color: '#dc3545' }}>
              {schedules.filter(s => s.status === 'overdue').length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.overdue}
            </text>
          </view>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 20, fontWeight: 'bold', color: '#ffc107' }}>
              {schedules.filter(s => s.status === 'pending').length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.pending}
            </text>
          </view>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 20, fontWeight: 'bold', color: '#28a745' }}>
              {payments.length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.payments}
            </text>
          </view>
        </view>
      </view>

      {/* Content */}
      <scroll-view style={{ flex: 1 }}>
        <view style={{ padding: 20 }}>
          {activeTab === 'schedule' ? (
            schedules.length === 0 ? (
              <view style={{ 
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: 40
              }}>
                <text style={{ fontSize: 16, color: '#666' }}>
                  {t.noSchedule}
                </text>
              </view>
            ) : (
              schedules.map((schedule) => (
                <ScheduleCard
                  key={schedule.id}
                  schedule={schedule}
                  language={language}
                  getStatusColor={getStatusColor}
                  getStatusText={getStatusText}
                  texts={t}
                />
              ))
            )
          ) : (
            payments.length === 0 ? (
              <view style={{ 
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: 40
              }}>
                <text style={{ fontSize: 16, color: '#666' }}>
                  {t.noPayments}
                </text>
              </view>
            ) : (
              payments.map((payment) => (
                <PaymentCard
                  key={payment.id}
                  payment={payment}
                  language={language}
                  getPaymentMethodText={getPaymentMethodText}
                />
              ))
            )
          )}
        </view>
      </scroll-view>

      {/* Record Payment Button */}
      <view 
        style={{
          position: 'absolute',
          bottom: 20,
          right: 20,
          width: 56,
          height: 56,
          backgroundColor: '#28a745',
          borderRadius: 28,
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <text style={{ color: 'white', fontSize: 24 }}>💳</text>
      </view>
    </view>
  )
}

function ScheduleCard({ schedule, language, getStatusColor, getStatusText, texts }: {
  schedule: LoanSchedule
  language: 'en' | 'km'
  getStatusColor: (status: string) => string
  getStatusText: (status: string) => string
  texts: any
}) {
  const daysUntilDue = getDaysUntilDue(schedule.dueDate)
  const isOverduePayment = isOverdue(schedule.dueDate)

  return (
    <view style={{
      backgroundColor: 'white',
      borderRadius: 12,
      padding: 15,
      marginBottom: 10,
      borderLeftWidth: 4,
      borderLeftColor: getStatusColor(schedule.status)
    }}>
      <view style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 10 }}>
        <view style={{ flex: 1 }}>
          <text style={{ 
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333',
            marginBottom: 5
          }}>
            {texts.installment} #{schedule.installmentNumber}
          </text>
          <text style={{ fontSize: 14, color: '#666', marginBottom: 3 }}>
            {texts.dueDate}: {formatDate(schedule.dueDate, language)}
          </text>
          <text style={{ fontSize: 14, color: '#666', marginBottom: 3 }}>
            {texts.total}: {formatCurrency(schedule.totalAmount)}
          </text>
          {schedule.remainingAmount > 0 && (
            <text style={{ fontSize: 14, color: '#dc3545' }}>
              {language === 'km' ? 'នៅសល់:' : 'Remaining:'} {formatCurrency(schedule.remainingAmount)}
            </text>
          )}
        </view>
        
        <view>
          <view style={{
            backgroundColor: getStatusColor(schedule.status),
            borderRadius: 12,
            paddingHorizontal: 8,
            paddingVertical: 4,
            marginBottom: 5
          }}>
            <text style={{ 
              color: 'white',
              fontSize: 12,
              fontWeight: '600'
            }}>
              {getStatusText(schedule.status)}
            </text>
          </view>
          
          {schedule.status === 'pending' && (
            <text style={{ 
              fontSize: 12, 
              color: isOverduePayment ? '#dc3545' : '#666',
              textAlign: 'center'
            }}>
              {isOverduePayment 
                ? `${Math.abs(daysUntilDue)} ${texts.daysOverdue}`
                : `${daysUntilDue} ${texts.daysDue}`
              }
            </text>
          )}
        </view>
      </view>
    </view>
  )
}

function PaymentCard({ payment, language, getPaymentMethodText }: {
  payment: Payment
  language: 'en' | 'km'
  getPaymentMethodText: (method: string) => string
}) {
  return (
    <view style={{
      backgroundColor: 'white',
      borderRadius: 12,
      padding: 15,
      marginBottom: 10,
      borderLeftWidth: 4,
      borderLeftColor: payment.isLate ? '#ffc107' : '#28a745'
    }}>
      <view style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <view style={{ flex: 1 }}>
          <text style={{ 
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333',
            marginBottom: 5
          }}>
            {formatCurrency(payment.amount)}
          </text>
          <text style={{ fontSize: 14, color: '#666', marginBottom: 3 }}>
            {formatDate(payment.paidAt, language)}
          </text>
          <text style={{ fontSize: 14, color: '#666', marginBottom: 3 }}>
            {getPaymentMethodText(payment.paymentMethod)}
          </text>
          <text style={{ fontSize: 14, color: '#666' }}>
            {language === 'km' ? 'បង្កាន់ដៃ:' : 'Receipt:'} {payment.receiptNumber}
          </text>
          {payment.lateFee && (
            <text style={{ fontSize: 14, color: '#dc3545', marginTop: 3 }}>
              {language === 'km' ? 'ពិន័យយឺតយ៉ាវ:' : 'Late fee:'} {formatCurrency(payment.lateFee)}
            </text>
          )}
        </view>
        
        {payment.isLate && (
          <view style={{
            backgroundColor: '#ffc107',
            borderRadius: 12,
            paddingHorizontal: 8,
            paddingVertical: 4
          }}>
            <text style={{ 
              color: 'white',
              fontSize: 12,
              fontWeight: '600'
            }}>
              {language === 'km' ? 'យឺត' : 'Late'}
            </text>
          </view>
        )}
      </view>
    </view>
  )
}
