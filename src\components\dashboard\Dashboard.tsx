import { useState, useEffect } from '@lynx-js/react'
import { Employee, DashboardStats } from '../../types'
import { useTheme } from '../../contexts/ThemeContext'
import { formatCurrency } from '../../utils/currency'
import { formatDate } from '../../utils/date'

interface DashboardProps {
  currentUser: Employee
  language: 'en' | 'km'
}

export function Dashboard({ currentUser, language }: DashboardProps) {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    // Simulate API call to fetch dashboard stats
    setTimeout(() => {
      setStats({
        totalActiveLoans: 156,
        totalLoanAmount: 2450000,
        totalCollected: 1890000,
        overdueLoans: 23,
        overdueAmount: 145000,
        newClientsThisMonth: 12,
        loansApprovedThisMonth: 34,
        collectionRate: 87.5,
        averageLoanSize: 15700
      })
      setIsLoading(false)
    }, 1000)
  }, [])

  const texts = {
    en: {
      title: 'Dashboard',
      overview: 'Overview',
      totalActiveLoans: 'Active Loans',
      totalLoanAmount: 'Total Loan Amount',
      totalCollected: 'Total Collected',
      overdueLoans: 'Overdue Loans',
      overdueAmount: 'Overdue Amount',
      newClients: 'New Clients This Month',
      loansApproved: 'Loans Approved This Month',
      collectionRate: 'Collection Rate',
      averageLoanSize: 'Average Loan Size',
      quickActions: 'Quick Actions',
      addClient: 'Add New Client',
      newLoan: 'Create New Loan',
      recordPayment: 'Record Payment',
      viewReports: 'View Reports',
      recentActivity: 'Recent Activity',
      loading: 'Loading...'
    },
    km: {
      title: 'ផ្ទាំងគ្រប់គ្រង',
      overview: 'ទិដ្ឋភាពទូទៅ',
      totalActiveLoans: 'កម្ចីសកម្ម',
      totalLoanAmount: 'ចំនួនកម្ចីសរុប',
      totalCollected: 'ចំនួនប្រមូលបាន',
      overdueLoans: 'កម្ចីយឺតយ៉ាវ',
      overdueAmount: 'ចំនួនយឺតយ៉ាវ',
      newClients: 'អតិថិជនថ្មីខែនេះ',
      loansApproved: 'កម្ចីអនុម័តខែនេះ',
      collectionRate: 'អត្រាប្រមូល',
      averageLoanSize: 'ទំហំកម្ចីមធ្យម',
      quickActions: 'សកម្មភាពរហ័ស',
      addClient: 'បន្ថែមអតិថិជនថ្មី',
      newLoan: 'បង្កើតកម្ចីថ្មី',
      recordPayment: 'កត់ត្រាការទូទាត់',
      viewReports: 'មើលរបាយការណ៍',
      recentActivity: 'សកម្មភាពថ្មីៗ',
      loading: 'កំពុងផ្ទុក...'
    }
  }

  const t = texts[language]

  if (isLoading) {
    return (
      <view className="flex-1 justify-center items-center">
        <view className="text-center">
          <view className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></view>
          <text className="text-lg text-secondary khmer-text">
            {t.loading}
          </text>
        </view>
      </view>
    )
  }

  if (!stats) return null

  return (
    <scroll-view className="flex-1">
      <view className="dashboard-container">
        {/* Floating decorations */}
        <view className="floating-decoration decoration-1" />
        <view className="floating-decoration decoration-2" />
        <view className="floating-decoration decoration-3" />
        <view className="floating-decoration decoration-4" />

        {/* Header */}
        <view className="dashboard-header animate-fade-in">
          <text className="dashboard-title khmer-text">
            {t.title}
          </text>
          <text className="dashboard-subtitle">
            {language === 'km' ? `សួស្តី ${currentUser.fullNameKhmer || currentUser.fullName}` : `Welcome, ${currentUser.fullName}`}
          </text>
          <text className="dashboard-date">
            {formatDate(new Date(), language)}
          </text>
        </view>

        {/* Stats Overview */}
        <view className="dashboard-section">
          <text className="dashboard-section-title animate-slide-in-left khmer-text">
            {t.overview}
          </text>

          <view className="stats-grid">
            <StatCard
              title={t.totalActiveLoans}
              value={stats.totalActiveLoans.toString()}
              variant="temple-blue"
              icon="💰"
              delay="0.1s"
            />
            <StatCard
              title={t.totalLoanAmount}
              value={formatCurrency(stats.totalLoanAmount)}
              variant="khmer-gold"
              icon="💎"
              delay="0.2s"
            />
            <StatCard
              title={t.totalCollected}
              value={formatCurrency(stats.totalCollected)}
              variant="lotus-pink"
              icon="💳"
              delay="0.3s"
            />
            <StatCard
              title={t.overdueLoans}
              value={stats.overdueLoans.toString()}
              variant="sunset"
              icon="⚠️"
              delay="0.4s"
            />
            <StatCard
              title={t.overdueAmount}
              value={formatCurrency(stats.overdueAmount)}
              variant="temple-blue"
              icon="📊"
              delay="0.5s"
            />
            <StatCard
              title={t.collectionRate}
              value={`${stats.collectionRate}%`}
              variant="khmer-gold"
              icon="📈"
              delay="0.6s"
            />
          </view>
        </view>

        {/* Quick Actions */}
        <text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#333',
          marginBottom: 15
        }}>
          {t.quickActions}
        </text>

        <view style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          marginBottom: 30
        }}>
          <ActionButton
            title={t.addClient}
            icon="👤"
            color="#007bff"
            onPress={() => { }}
          />
          <ActionButton
            title={t.newLoan}
            icon="💰"
            color="#28a745"
            onPress={() => { }}
          />
          <ActionButton
            title={t.recordPayment}
            icon="💳"
            color="#17a2b8"
            onPress={() => { }}
          />
          <ActionButton
            title={t.viewReports}
            icon="📊"
            color="#6f42c1"
            onPress={() => { }}
          />
        </view>

        {/* Recent Activity */}
        <text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#333',
          marginBottom: 15
        }}>
          {t.recentActivity}
        </text>

        <view style={{
          backgroundColor: 'white',
          borderRadius: 12,
          padding: 15
        }}>
          <ActivityItem
            title={language === 'km' ? 'កម្ចីថ្មីត្រូវបានអនុម័ត' : 'New loan approved'}
            subtitle={language === 'km' ? 'សុខ សុភា - $5,000' : 'Sok Sophea - $5,000'}
            time="2h ago"
          />
          <ActivityItem
            title={language === 'km' ? 'ការទូទាត់ត្រូវបានកត់ត្រា' : 'Payment recorded'}
            subtitle={language === 'km' ? 'ចាន់ ដារា - $250' : 'Chan Dara - $250'}
            time="4h ago"
          />
          <ActivityItem
            title={language === 'km' ? 'អតិថិជនថ្មីបានចុះឈ្មោះ' : 'New client registered'}
            subtitle={language === 'km' ? 'លី មុនី' : 'Li Mony'}
            time="6h ago"
          />
        </view>
      </view>
    </scroll-view>
  )
}

function StatCard({ title, value, variant, icon, delay }: {
  title: string
  value: string
  variant: 'temple-blue' | 'khmer-gold' | 'lotus-pink' | 'sunset'
  icon: string
  delay: string
}) {
  return (
    <view
      className={`stat-card-enhanced stat-card-${variant} animate-stagger`}
      style={{ animationDelay: delay }}
    >
      <view className={`stat-icon stat-icon-${variant} animate-floating`}>
        <text>{icon}</text>
      </view>

      <text className="stat-value animate-fade-in">
        {value}
      </text>

      <text className="stat-label khmer-text mb-2">
        {title}
      </text>

      <view className="stat-change stat-change-positive mt-2">
        <text className="text-xs">+12% from last month</text>
      </view>
    </view>
  )
}

function ActionButton({ title, icon, color, onPress }: {
  title: string
  icon: string
  color: string
  onPress: () => void
}) {
  return (
    <view
      style={{
        width: '48%',
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 15,
        margin: '1%',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: color
      }}
      bindtap={onPress}
    >
      <text style={{ fontSize: 24, marginBottom: 8 }}>
        {icon}
      </text>
      <text style={{
        fontSize: 14,
        color: color,
        fontWeight: '600',
        textAlign: 'center'
      }}>
        {title}
      </text>
    </view>
  )
}

function ActivityItem({ title, subtitle, time }: {
  title: string
  subtitle: string
  time: string
}) {
  return (
    <view style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 10,
      borderBottomWidth: 1,
      borderBottomColor: '#f0f0f0'
    }}>
      <view style={{
        width: 8,
        height: 8,
        backgroundColor: '#007bff',
        borderRadius: 4,
        marginRight: 12
      }} />
      <view style={{ flex: 1 }}>
        <text style={{
          fontSize: 14,
          fontWeight: '600',
          color: '#333',
          marginBottom: 2
        }}>
          {title}
        </text>
        <text style={{
          fontSize: 12,
          color: '#666'
        }}>
          {subtitle}
        </text>
      </view>
      <text style={{
        fontSize: 12,
        color: '#999'
      }}>
        {time}
      </text>
    </view>
  )
}
