// Badge Components with <PERSON><PERSON><PERSON> and <PERSON> Effects

// ===== BASE BADGE =====
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-1 $spacing-3;
  border-radius: $border-radius-full;
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all $transition-base;
  
  // Icon support
  .badge-icon {
    margin-right: $spacing-1;
    
    &.badge-icon-right {
      margin-right: 0;
      margin-left: $spacing-1;
    }
    
    &.badge-icon-only {
      margin: 0;
    }
  }
}

// ===== GRADIENT BADGES =====
.badge-primary {
  @include gradient-temple-blue;
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: $shadow-temple-blue;
  }
}

.badge-secondary {
  @include gradient-khmer-gold;
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: $shadow-khmer-gold;
  }
}

.badge-accent {
  @include gradient-lotus-pink;
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: $shadow-lotus-pink;
  }
}

.badge-success {
  background: linear-gradient(135deg, $success 0%, darken($success, 10%) 100%);
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba($success, 0.3);
  }
}

.badge-warning {
  @include gradient-khmer-gold;
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: $shadow-khmer-gold;
  }
}

.badge-danger {
  background: linear-gradient(135deg, $danger 0%, darken($danger, 10%) 100%);
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba($danger, 0.3);
  }
}

.badge-info {
  @include gradient-temple-blue;
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: $shadow-temple-blue;
  }
}

// ===== SPECIAL GRADIENT BADGES =====
.badge-sunset {
  @include gradient-sunset;
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba($khmer-gold-500, 0.3);
  }
}

.badge-temple {
  @include gradient-temple;
  color: $white;
  @include text-shadow-soft;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba($temple-blue-500, 0.3);
  }
}

// ===== OUTLINE BADGES =====
.badge-outline {
  background: transparent;
  border: 2px solid $gray-300;
  color: $gray-700;
  
  &:hover {
    background: $gray-50;
    transform: scale(1.05);
  }
}

.badge-outline-primary {
  @extend .badge-outline;
  border-color: $primary;
  color: $primary;
  
  &:hover {
    background: alpha($primary, 0.1);
  }
}

.badge-outline-secondary {
  @extend .badge-outline;
  border-color: $secondary;
  color: $secondary;
  
  &:hover {
    background: alpha($secondary, 0.1);
  }
}

.badge-outline-accent {
  @extend .badge-outline;
  border-color: $accent;
  color: $accent;
  
  &:hover {
    background: alpha($accent, 0.1);
  }
}

.badge-outline-success {
  @extend .badge-outline;
  border-color: $success;
  color: $success;
  
  &:hover {
    background: alpha($success, 0.1);
  }
}

.badge-outline-warning {
  @extend .badge-outline;
  border-color: $warning;
  color: $warning;
  
  &:hover {
    background: alpha($warning, 0.1);
  }
}

.badge-outline-danger {
  @extend .badge-outline;
  border-color: $danger;
  color: $danger;
  
  &:hover {
    background: alpha($danger, 0.1);
  }
}

.badge-outline-info {
  @extend .badge-outline;
  border-color: $info;
  color: $info;
  
  &:hover {
    background: alpha($info, 0.1);
  }
}

// ===== GLASS BADGES =====
.badge-glass {
  @include glass-morphism;
  color: $gray-800;
  
  &:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
    box-shadow: $shadow-md;
  }
}

.badge-glass-dark {
  @include glass-morphism-dark;
  color: $white;
  
  &:hover {
    background: rgba(0, 0, 0, 0.4);
    transform: scale(1.05);
    box-shadow: $shadow-md;
  }
}

// ===== BADGE SIZES =====
.badge-xs {
  padding: $spacing-1 $spacing-2;
  font-size: 0.625rem; // 10px
}

.badge-sm {
  padding: $spacing-1 $spacing-3;
  font-size: $font-size-xs;
}

.badge-md {
  padding: $spacing-2 $spacing-4;
  font-size: $font-size-sm;
}

.badge-lg {
  padding: $spacing-3 $spacing-5;
  font-size: $font-size-base;
}

// ===== BADGE SHAPES =====
.badge-rounded {
  border-radius: $border-radius-lg;
}

.badge-square {
  border-radius: $border-radius-sm;
}

.badge-circle {
  border-radius: 50%;
  width: 24px;
  height: 24px;
  padding: 0;
  
  &.badge-sm {
    width: 20px;
    height: 20px;
  }
  
  &.badge-lg {
    width: 32px;
    height: 32px;
  }
}

// ===== NOTIFICATION BADGES =====
.badge-notification {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 20px;
  height: 20px;
  padding: 0 $spacing-1;
  background: $danger;
  color: $white;
  font-size: 0.625rem;
  font-weight: $font-weight-bold;
  border-radius: $border-radius-full;
  @include flex-center;
  @include pulse-animation;
  
  &.badge-notification-lg {
    top: -12px;
    right: -12px;
    min-width: 24px;
    height: 24px;
    font-size: $font-size-xs;
  }
}

// ===== STATUS BADGES =====
.badge-status {
  position: relative;
  padding-left: $spacing-4;
  
  &::before {
    content: '';
    position: absolute;
    left: $spacing-2;
    top: 50%;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    transform: translateY(-50%);
  }
  
  &.badge-status-online::before {
    background: $success;
    @include pulse-animation;
  }
  
  &.badge-status-offline::before {
    background: $gray-400;
  }
  
  &.badge-status-busy::before {
    background: $danger;
  }
  
  &.badge-status-away::before {
    background: $warning;
  }
}

// ===== ANIMATED BADGES =====
.badge-shimmer {
  @include shimmer-effect;
  overflow: hidden;
}

.badge-pulse {
  @include pulse-animation;
}

.badge-floating {
  @include floating-animation;
}

.badge-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

// ===== INTERACTIVE BADGES =====
.badge-clickable {
  cursor: pointer;
  
  &:hover {
    transform: translateY(-1px) scale(1.05);
  }
  
  &:active {
    transform: translateY(0) scale(1);
  }
}

.badge-removable {
  padding-right: $spacing-5;
  position: relative;
  
  &::after {
    content: '×';
    position: absolute;
    right: $spacing-2;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    opacity: 0.7;
    transition: opacity $transition-base;
    
    &:hover {
      opacity: 1;
    }
  }
}

// ===== BADGE GROUPS =====
.badge-group {
  display: inline-flex;
  gap: $spacing-2;
  flex-wrap: wrap;
  
  .badge {
    margin: 0;
  }
}

.badge-group-stacked {
  display: inline-flex;
  
  .badge {
    margin-left: -$spacing-1;
    border: 2px solid $white;
    
    &:first-child {
      margin-left: 0;
    }
    
    &:hover {
      z-index: 1;
      transform: scale(1.1);
    }
  }
}

// ===== RESPONSIVE BADGES =====
@include mobile-only {
  .badge {
    font-size: 0.625rem;
    padding: $spacing-1 $spacing-2;
  }
  
  .badge-lg {
    font-size: $font-size-xs;
    padding: $spacing-2 $spacing-3;
  }
  
  .badge-notification {
    min-width: 18px;
    height: 18px;
    font-size: 0.5rem;
  }
}
