import { useState, useEffect, useCallback } from '@lynx-js/react'
import { Employee, Client, ClientSearchFilters } from '../../types'
import { formatDate } from '../../utils/date'

interface ClientManagementProps {
  currentUser: Employee
  language: 'en' | 'km'
}

// Mock client data
const MOCK_CLIENTS: Client[] = [
  {
    id: '1',
    firstName: 'Sok',
    lastName: 'Sophea',
    firstNameKhmer: 'សុខ',
    lastNameKhmer: 'សុភា',
    nationalId: '123456789012',
    phoneNumber: '+855123456789',
    address: '123 Street 271, Sangkat Toul Tompong',
    addressKhmer: '១២៣ ផ្លូវ២៧១ សង្កាត់ទួលទំពូង',
    province: 'Phnom Penh',
    district: 'Chamkar Mon',
    commune: 'Toul Tompong',
    village: 'Toul Tompong 1',
    occupation: 'Teacher',
    occupationKhmer: 'គ្រូបង្រៀន',
    monthlyIncome: 800,
    emergencyContact: {
      name: '<PERSON><PERSON>',
      nameKhmer: 'សុខ ដារា',
      relationship: 'Spouse',
      phoneNumber: '+855987654321'
    },
    isActive: true,
    totalLoans: 2,
    activeLoans: 1,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-06-20'),
    createdBy: 'admin'
  },
  {
    id: '2',
    firstName: 'Chan',
    lastName: 'Dara',
    firstNameKhmer: 'ចាន់',
    lastNameKhmer: 'ដារា',
    nationalId: '987654321098',
    phoneNumber: '+855987654321',
    address: '456 Street 63, Sangkat Boeung Keng Kang',
    province: 'Phnom Penh',
    district: 'Chamkar Mon',
    commune: 'Boeung Keng Kang',
    village: 'Boeung Keng Kang 1',
    occupation: 'Business Owner',
    occupationKhmer: 'ម្ចាស់អាជីវកម្ម',
    monthlyIncome: 1500,
    emergencyContact: {
      name: 'Chan Mony',
      relationship: 'Brother',
      phoneNumber: '+855555666777'
    },
    isActive: true,
    totalLoans: 1,
    activeLoans: 1,
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date('2024-06-15'),
    createdBy: 'manager'
  }
]

export function ClientManagement({ currentUser, language }: ClientManagementProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [filteredClients, setFilteredClients] = useState<Client[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setClients(MOCK_CLIENTS)
      setFilteredClients(MOCK_CLIENTS)
      setIsLoading(false)
    }, 1000)
  }, [])

  useEffect(() => {
    // Filter clients based on search query
    if (!searchQuery.trim()) {
      setFilteredClients(clients)
    } else {
      const filtered = clients.filter(client => {
        const fullName = `${client.firstName} ${client.lastName}`.toLowerCase()
        const fullNameKhmer = client.firstNameKhmer && client.lastNameKhmer 
          ? `${client.firstNameKhmer} ${client.lastNameKhmer}` 
          : ''
        const query = searchQuery.toLowerCase()
        
        return fullName.includes(query) ||
               fullNameKhmer.includes(query) ||
               client.nationalId.includes(query) ||
               client.phoneNumber.includes(query)
      })
      setFilteredClients(filtered)
    }
  }, [searchQuery, clients])

  const handleAddClient = useCallback(() => {
    setShowAddForm(true)
    setSelectedClient(null)
  }, [])

  const handleEditClient = useCallback((client: Client) => {
    setSelectedClient(client)
    setShowAddForm(true)
  }, [])

  const handleViewClient = useCallback((client: Client) => {
    setSelectedClient(client)
    setShowAddForm(false)
  }, [])

  const texts = {
    en: {
      title: 'Client Management',
      search: 'Search clients...',
      addClient: 'Add New Client',
      totalClients: 'Total Clients',
      activeClients: 'Active Clients',
      name: 'Name',
      phone: 'Phone',
      nationalId: 'National ID',
      loans: 'Loans',
      status: 'Status',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      active: 'Active',
      inactive: 'Inactive',
      loading: 'Loading clients...',
      noClients: 'No clients found'
    },
    km: {
      title: 'គ្រប់គ្រងអតិថិជន',
      search: 'ស្វែងរកអតិថិជន...',
      addClient: 'បន្ថែមអតិថិជនថ្មី',
      totalClients: 'អតិថិជនសរុប',
      activeClients: 'អតិថិជនសកម្ម',
      name: 'ឈ្មោះ',
      phone: 'លេខទូរស័ព្ទ',
      nationalId: 'អត្តសញ្ញាណប័ណ្ណ',
      loans: 'កម្ចី',
      status: 'ស្ថានភាព',
      actions: 'សកម្មភាព',
      view: 'មើល',
      edit: 'កែប្រែ',
      active: 'សកម្ម',
      inactive: 'មិនសកម្ម',
      loading: 'កំពុងផ្ទុកអតិថិជន...',
      noClients: 'រកមិនឃើញអតិថិជន'
    }
  }

  const t = texts[language]

  if (isLoading) {
    return (
      <view style={{ 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center' 
      }}>
        <text style={{ fontSize: 18, color: '#666' }}>
          {t.loading}
        </text>
      </view>
    )
  }

  if (showAddForm) {
    return (
      <ClientForm
        client={selectedClient}
        language={language}
        onSave={(client) => {
          // Handle save
          setShowAddForm(false)
          setSelectedClient(null)
        }}
        onCancel={() => {
          setShowAddForm(false)
          setSelectedClient(null)
        }}
      />
    )
  }

  if (selectedClient && !showAddForm) {
    return (
      <ClientDetail
        client={selectedClient}
        language={language}
        onEdit={() => setShowAddForm(true)}
        onBack={() => setSelectedClient(null)}
      />
    )
  }

  return (
    <view style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <view style={{ 
        backgroundColor: 'white',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0'
      }}>
        <text style={{ 
          fontSize: 24,
          fontWeight: 'bold',
          color: '#333',
          marginBottom: 15
        }}>
          {t.title}
        </text>

        {/* Search Bar */}
        <input
          style={{
            width: '100%',
            height: 40,
            borderWidth: 1,
            borderColor: '#ddd',
            borderRadius: 8,
            paddingHorizontal: 12,
            fontSize: 16,
            backgroundColor: '#f8f9fa',
            marginBottom: 15
          }}
          value={searchQuery}
          onInput={(e) => setSearchQuery(e.detail.value)}
          placeholder={t.search}
        />

        {/* Stats */}
        <view style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 24, fontWeight: 'bold', color: '#007bff' }}>
              {clients.length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.totalClients}
            </text>
          </view>
          <view style={{ alignItems: 'center' }}>
            <text style={{ fontSize: 24, fontWeight: 'bold', color: '#28a745' }}>
              {clients.filter(c => c.isActive).length}
            </text>
            <text style={{ fontSize: 12, color: '#666' }}>
              {t.activeClients}
            </text>
          </view>
        </view>
      </view>

      {/* Client List */}
      <scroll-view style={{ flex: 1 }}>
        <view style={{ padding: 20 }}>
          {filteredClients.length === 0 ? (
            <view style={{ 
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 40
            }}>
              <text style={{ fontSize: 16, color: '#666' }}>
                {t.noClients}
              </text>
            </view>
          ) : (
            filteredClients.map((client) => (
              <ClientCard
                key={client.id}
                client={client}
                language={language}
                onView={() => handleViewClient(client)}
                onEdit={() => handleEditClient(client)}
              />
            ))
          )}
        </view>
      </scroll-view>

      {/* Add Button */}
      <view 
        style={{
          position: 'absolute',
          bottom: 20,
          right: 20,
          width: 56,
          height: 56,
          backgroundColor: '#007bff',
          borderRadius: 28,
          justifyContent: 'center',
          alignItems: 'center'
        }}
        bindtap={handleAddClient}
      >
        <text style={{ color: 'white', fontSize: 24 }}>+</text>
      </view>
    </view>
  )
}

function ClientCard({ client, language, onView, onEdit }: {
  client: Client
  language: 'en' | 'km'
  onView: () => void
  onEdit: () => void
}) {
  const displayName = language === 'km' && client.firstNameKhmer && client.lastNameKhmer
    ? `${client.firstNameKhmer} ${client.lastNameKhmer}`
    : `${client.firstName} ${client.lastName}`

  return (
    <view style={{
      backgroundColor: 'white',
      borderRadius: 12,
      padding: 15,
      marginBottom: 10,
      borderLeftWidth: 4,
      borderLeftColor: client.isActive ? '#28a745' : '#dc3545'
    }}>
      <view style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <view style={{ flex: 1 }}>
          <text style={{ 
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333',
            marginBottom: 5
          }}>
            {displayName}
          </text>
          <text style={{ fontSize: 14, color: '#666', marginBottom: 3 }}>
            📞 {client.phoneNumber}
          </text>
          <text style={{ fontSize: 14, color: '#666', marginBottom: 3 }}>
            🆔 {client.nationalId}
          </text>
          <text style={{ fontSize: 14, color: '#666' }}>
            💰 {client.activeLoans}/{client.totalLoans} {language === 'km' ? 'កម្ចី' : 'loans'}
          </text>
        </view>
        
        <view style={{ flexDirection: 'row' }}>
          <text 
            style={{ 
              color: '#007bff',
              fontSize: 14,
              marginRight: 15
            }}
            bindtap={onView}
          >
            {language === 'km' ? 'មើល' : 'View'}
          </text>
          <text 
            style={{ 
              color: '#28a745',
              fontSize: 14
            }}
            bindtap={onEdit}
          >
            {language === 'km' ? 'កែប្រែ' : 'Edit'}
          </text>
        </view>
      </view>
    </view>
  )
}

// Placeholder components - will be implemented in next steps
function ClientForm({ client, language, onSave, onCancel }: {
  client: Client | null
  language: 'en' | 'km'
  onSave: (client: Client) => void
  onCancel: () => void
}) {
  return (
    <view style={{ flex: 1, padding: 20 }}>
      <text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
        {language === 'km' ? 'បន្ថែម/កែប្រែអតិថិជន' : 'Add/Edit Client'}
      </text>
      <text style={{ fontSize: 16, color: '#666' }}>
        {language === 'km' ? 'ទម្រង់នេះនឹងត្រូវបានអភិវឌ្ឍនៅជំហានបន្ទាប់' : 'This form will be implemented in the next step'}
      </text>
      <view 
        style={{
          backgroundColor: '#007bff',
          borderRadius: 8,
          height: 48,
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: 20
        }}
        bindtap={onCancel}
      >
        <text style={{ color: 'white', fontSize: 16 }}>
          {language === 'km' ? 'ត្រលប់' : 'Back'}
        </text>
      </view>
    </view>
  )
}

function ClientDetail({ client, language, onEdit, onBack }: {
  client: Client
  language: 'en' | 'km'
  onEdit: () => void
  onBack: () => void
}) {
  const displayName = language === 'km' && client.firstNameKhmer && client.lastNameKhmer
    ? `${client.firstNameKhmer} ${client.lastNameKhmer}`
    : `${client.firstName} ${client.lastName}`

  return (
    <scroll-view style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      <view style={{ padding: 20 }}>
        <view style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 20 }}>
          <text 
            style={{ fontSize: 18, color: '#007bff', marginRight: 15 }}
            bindtap={onBack}
          >
            ← {language === 'km' ? 'ត្រលប់' : 'Back'}
          </text>
          <text style={{ fontSize: 24, fontWeight: 'bold', color: '#333', flex: 1 }}>
            {displayName}
          </text>
          <text 
            style={{ fontSize: 16, color: '#28a745' }}
            bindtap={onEdit}
          >
            {language === 'km' ? 'កែប្រែ' : 'Edit'}
          </text>
        </view>

        <view style={{
          backgroundColor: 'white',
          borderRadius: 12,
          padding: 20
        }}>
          <text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 15 }}>
            {language === 'km' ? 'ព័ត៌មានលម្អិត' : 'Client Details'}
          </text>
          
          <view style={{ marginBottom: 10 }}>
            <text style={{ fontSize: 14, color: '#666' }}>
              {language === 'km' ? 'លេខទូរស័ព្ទ:' : 'Phone:'} {client.phoneNumber}
            </text>
          </view>
          
          <view style={{ marginBottom: 10 }}>
            <text style={{ fontSize: 14, color: '#666' }}>
              {language === 'km' ? 'អត្តសញ្ញាណប័ណ្ណ:' : 'National ID:'} {client.nationalId}
            </text>
          </view>
          
          <view style={{ marginBottom: 10 }}>
            <text style={{ fontSize: 14, color: '#666' }}>
              {language === 'km' ? 'អាជីព:' : 'Occupation:'} {language === 'km' ? client.occupationKhmer || client.occupation : client.occupation}
            </text>
          </view>
          
          <view style={{ marginBottom: 10 }}>
            <text style={{ fontSize: 14, color: '#666' }}>
              {language === 'km' ? 'ចំណូលប្រចាំខែ:' : 'Monthly Income:'} ${client.monthlyIncome}
            </text>
          </view>
          
          <view style={{ marginBottom: 10 }}>
            <text style={{ fontSize: 14, color: '#666' }}>
              {language === 'km' ? 'កម្ចីសកម្ម:' : 'Active Loans:'} {client.activeLoans}
            </text>
          </view>
        </view>
      </view>
    </scroll-view>
  )
}
