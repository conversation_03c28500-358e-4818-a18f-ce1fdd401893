// Dark Theme for Khmer Loan Management App

[data-theme="dark"] {
  // ===== SEMANTIC COLORS =====
  --color-primary: #{$temple-blue-400};
  --color-primary-hover: #{$temple-blue-300};
  --color-primary-light: #{$temple-blue-900};
  
  --color-secondary: #{$khmer-gold-400};
  --color-secondary-hover: #{$khmer-gold-300};
  --color-secondary-light: #{$khmer-gold-900};
  
  --color-accent: #{$lotus-pink-400};
  --color-accent-hover: #{$lotus-pink-300};
  --color-accent-light: #{$lotus-pink-900};
  
  --color-success: #{lighten($success, 10%)};
  --color-warning: #{$khmer-gold-400};
  --color-danger: #{lighten($danger, 10%)};
  --color-info: #{$temple-blue-400};
  
  // ===== BACKGROUND COLORS =====
  --bg-primary: #{$gray-900};
  --bg-secondary: #{$gray-800};
  --bg-tertiary: #{$gray-700};
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  // ===== TEXT COLORS =====
  --text-primary: #{$gray-100};
  --text-secondary: #{$gray-300};
  --text-tertiary: #{$gray-400};
  --text-muted: #{$gray-500};
  --text-inverse: #{$gray-900};
  
  // ===== BORDER COLORS =====
  --border-primary: #{$gray-700};
  --border-secondary: #{$gray-600};
  --border-focus: #{$temple-blue-400};
  
  // ===== SHADOW COLORS =====
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-color-strong: rgba(0, 0, 0, 0.5);
  
  // ===== GLASS MORPHISM =====
  --glass-bg: rgba(0, 0, 0, 0.25);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-backdrop: blur(16px);
  
  // ===== GRADIENTS =====
  --gradient-primary: linear-gradient(135deg, #{$temple-blue-400} 0%, #{$temple-blue-600} 100%);
  --gradient-secondary: linear-gradient(135deg, #{$khmer-gold-400} 0%, #{$khmer-gold-600} 100%);
  --gradient-accent: linear-gradient(135deg, #{$lotus-pink-400} 0%, #{$lotus-pink-600} 100%);
  --gradient-sunset: linear-gradient(135deg, #{$khmer-gold-400} 0%, #{$lotus-pink-500} 50%, #{$temple-blue-500} 100%);
  --gradient-temple: linear-gradient(135deg, #{$temple-blue-400} 0%, #{$khmer-gold-400} 100%);
  
  // ===== COMPONENT SPECIFIC =====
  --navbar-bg: rgba(0, 0, 0, 0.9);
  --sidebar-bg: rgba(0, 0, 0, 0.95);
  --card-bg: #{$gray-800};
  --input-bg: #{$gray-800};
  --button-bg: #{$gray-700};
  
  // ===== KHMER PATTERN BACKGROUND =====
  --pattern-primary: rgba(#{red($khmer-gold-400)}, #{green($khmer-gold-400)}, #{blue($khmer-gold-400)}, 0.1);
  --pattern-secondary: rgba(#{red($temple-blue-400)}, #{green($temple-blue-400)}, #{blue($temple-blue-400)}, 0.1);
  --pattern-tertiary: rgba(#{red($lotus-pink-400)}, #{green($lotus-pink-400)}, #{blue($lotus-pink-400)}, 0.05);
}

// ===== DARK THEME SPECIFIC STYLES =====
.theme-dark {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  
  // Enhanced background pattern for dark theme
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      // Khmer temple silhouettes with glow
      radial-gradient(circle at 20% 20%, var(--pattern-primary) 0%, transparent 40%),
      radial-gradient(circle at 80% 80%, var(--pattern-secondary) 0%, transparent 40%),
      radial-gradient(circle at 40% 60%, var(--pattern-tertiary) 0%, transparent 30%),
      // Mystical lotus patterns
      radial-gradient(ellipse at 60% 20%, var(--pattern-primary) 0%, transparent 25%),
      radial-gradient(ellipse at 20% 80%, var(--pattern-secondary) 0%, transparent 25%),
      // Starfield effect
      radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.1) 0%, transparent 2%),
      radial-gradient(circle at 90% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 1%),
      radial-gradient(circle at 30% 90%, rgba(255, 255, 255, 0.08) 0%, transparent 1.5%);
    background-size: 
      800px 800px,
      600px 600px,
      400px 400px,
      300px 300px,
      350px 350px,
      100px 100px,
      150px 150px,
      120px 120px;
    background-position: 
      0 0,
      200px 200px,
      400px 100px,
      100px 300px,
      500px 400px,
      0 0,
      300px 100px,
      600px 300px;
    animation: float-pattern-dark 40s ease-in-out infinite;
    pointer-events: none;
    z-index: -2;
  }
  
  // Glowing decorative elements
  .floating-decoration {
    position: fixed;
    pointer-events: none;
    z-index: -1;
    
    &.decoration-1 {
      top: 10%;
      left: 5%;
      width: 20px;
      height: 20px;
      background: var(--pattern-primary);
      border-radius: 50%;
      box-shadow: 0 0 20px var(--pattern-primary);
      animation: floating 8s ease-in-out infinite, glow-pulse 4s ease-in-out infinite;
    }
    
    &.decoration-2 {
      top: 30%;
      right: 10%;
      width: 15px;
      height: 15px;
      background: var(--pattern-secondary);
      border-radius: 50%;
      box-shadow: 0 0 15px var(--pattern-secondary);
      animation: floating 6s ease-in-out infinite 1s, glow-pulse 5s ease-in-out infinite 1s;
    }
    
    &.decoration-3 {
      bottom: 20%;
      left: 15%;
      width: 25px;
      height: 25px;
      background: var(--pattern-tertiary);
      border-radius: 50%;
      box-shadow: 0 0 25px var(--pattern-tertiary);
      animation: floating 10s ease-in-out infinite 2s, glow-pulse 6s ease-in-out infinite 2s;
    }
    
    &.decoration-4 {
      bottom: 40%;
      right: 20%;
      width: 18px;
      height: 18px;
      background: var(--pattern-primary);
      border-radius: 50%;
      box-shadow: 0 0 18px var(--pattern-primary);
      animation: floating 7s ease-in-out infinite 3s, glow-pulse 3.5s ease-in-out infinite 3s;
    }
  }
}

// ===== COMPONENT OVERRIDES FOR DARK THEME =====
.theme-dark {
  .navbar {
    background: var(--navbar-bg);
    backdrop-filter: var(--glass-backdrop);
    border-bottom: 1px solid var(--border-primary);
  }
  
  .sidebar {
    background: var(--sidebar-bg);
    backdrop-filter: var(--glass-backdrop);
    border-right: 1px solid var(--border-primary);
  }
  
  .card {
    background: var(--card-bg);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 6px var(--shadow-color);
  }
  
  .card-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
  }
  
  .form-control {
    background: var(--input-bg);
    border: 2px solid var(--border-primary);
    color: var(--text-primary);
    
    &:focus {
      border-color: var(--border-focus);
      box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.2);
    }
    
    &::placeholder {
      color: var(--text-muted);
    }
  }
  
  .btn {
    &.btn-primary {
      background: var(--gradient-primary);
      color: var(--text-inverse);
      border: none;
    }
    
    &.btn-secondary {
      background: var(--gradient-secondary);
      color: var(--text-inverse);
      border: none;
    }
    
    &.btn-accent {
      background: var(--gradient-accent);
      color: var(--text-inverse);
      border: none;
    }
  }
  
  .badge {
    &.badge-primary {
      background: var(--gradient-primary);
      color: var(--text-inverse);
    }
    
    &.badge-secondary {
      background: var(--gradient-secondary);
      color: var(--text-inverse);
    }
    
    &.badge-accent {
      background: var(--gradient-accent);
      color: var(--text-inverse);
    }
  }
  
  .nav-link {
    color: var(--text-secondary);
    
    &:hover {
      color: var(--color-primary);
      background: var(--color-primary-light);
    }
    
    &.nav-link-active {
      color: var(--color-primary);
      background: var(--color-primary-light);
    }
  }
  
  .text-primary { color: var(--text-primary); }
  .text-secondary { color: var(--text-secondary); }
  .text-tertiary { color: var(--text-tertiary); }
  .text-muted { color: var(--text-muted); }
  .text-inverse { color: var(--text-inverse); }
  
  .bg-primary { background-color: var(--bg-primary); }
  .bg-secondary { background-color: var(--bg-secondary); }
  .bg-tertiary { background-color: var(--bg-tertiary); }
  
  .border-primary { border-color: var(--border-primary); }
  .border-secondary { border-color: var(--border-secondary); }
  .border-focus { border-color: var(--border-focus); }
  
  // Enhanced scrollbar for dark theme
  ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    
    &:hover {
      background: var(--text-muted);
    }
  }
}

// ===== DARK THEME ANIMATIONS =====
@keyframes float-pattern-dark {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translate(40px, -30px) rotate(2deg) scale(1.1);
    opacity: 1;
  }
  50% {
    transform: translate(-30px, 40px) rotate(-2deg) scale(0.9);
    opacity: 0.6;
  }
  75% {
    transform: translate(30px, -20px) rotate(1deg) scale(1.05);
    opacity: 0.9;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 10px currentColor;
    opacity: 0.8;
  }
  50% {
    box-shadow: 0 0 30px currentColor, 0 0 40px currentColor;
    opacity: 1;
  }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
@media (prefers-reduced-motion: reduce) {
  .theme-dark {
    &::before {
      animation: none;
    }
    
    .floating-decoration {
      animation: none;
    }
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {
  .theme-dark {
    --border-primary: #{$gray-500};
    --border-secondary: #{$gray-400};
    --text-primary: #{$white};
    --text-secondary: #{$gray-200};
    --shadow-color: rgba(0, 0, 0, 0.6);
    --bg-primary: #{$black};
    --bg-secondary: #{$gray-900};
  }
}
