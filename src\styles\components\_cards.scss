// Glass Morphism Cards and Components

// ===== BASE CARD STYLES =====
.card {
  @include card-base;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

// ===== GLASS MORPHISM CARDS =====
.card-glass {
  @include card-glass;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-xl;
    background: rgba(255, 255, 255, 0.35);
  }
}

.card-glass-dark {
  @include glass-morphism-dark;
  border-radius: $border-radius-xl;
  padding: $spacing-6;
  transition: all $transition-base;
  color: $white;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-xl;
    background: rgba(0, 0, 0, 0.35);
  }
}

// ===== GRADIENT CARDS =====
.card-gradient-khmer-gold {
  @include card-base;
  @include gradient-khmer-gold;
  color: $white;
  border: none;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-khmer-gold;
  }
}

.card-gradient-temple-blue {
  @include card-base;
  @include gradient-temple-blue;
  color: $white;
  border: none;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-temple-blue;
  }
}

.card-gradient-lotus-pink {
  @include card-base;
  @include gradient-lotus-pink;
  color: $white;
  border: none;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-lotus-pink;
  }
}

.card-gradient-sunset {
  @include card-base;
  @include gradient-sunset;
  color: $white;
  border: none;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba($khmer-gold-500, 0.2);
  }
}

.card-gradient-temple {
  @include card-base;
  @include gradient-temple;
  color: $white;
  border: none;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba($temple-blue-500, 0.2);
  }
}

// ===== CARD VARIANTS =====
.card-elevated {
  @include card-base;
  box-shadow: $shadow-lg;
  
  &:hover {
    transform: translateY(-6px);
    box-shadow: $shadow-2xl;
  }
}

.card-floating {
  @include card-base;
  @include floating-animation;
  
  &:hover {
    animation-play-state: paused;
    transform: translateY(-8px);
    box-shadow: $shadow-xl;
  }
}

.card-shimmer {
  @include card-base;
  @include shimmer-effect;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-lg;
  }
}

// ===== CARD SIZES =====
.card-sm {
  padding: $spacing-4;
  border-radius: $border-radius-lg;
}

.card-md {
  padding: $spacing-6;
  border-radius: $border-radius-xl;
}

.card-lg {
  padding: $spacing-8;
  border-radius: $border-radius-xl;
}

.card-xl {
  padding: $spacing-10;
  border-radius: $border-radius-xl;
}

// ===== CARD HEADER =====
.card-header {
  border-bottom: 1px solid $gray-200;
  padding-bottom: $spacing-4;
  margin-bottom: $spacing-6;
  
  .card-glass & {
    border-bottom-color: rgba(255, 255, 255, 0.2);
  }
  
  .card-glass-dark & {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
}

.card-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin: 0;
  @include khmer-text;
  @include text-shadow-soft;
  
  .card-glass & {
    color: $gray-800;
  }
  
  .card-glass-dark & {
    color: $white;
  }
  
  .card-gradient-khmer-gold &,
  .card-gradient-temple-blue &,
  .card-gradient-lotus-pink &,
  .card-gradient-sunset &,
  .card-gradient-temple & {
    color: $white;
    @include text-shadow-strong;
  }
}

.card-subtitle {
  font-size: $font-size-sm;
  color: $gray-600;
  margin: $spacing-1 0 0 0;
  @include khmer-text;
  
  .card-glass & {
    color: $gray-700;
  }
  
  .card-glass-dark & {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .card-gradient-khmer-gold &,
  .card-gradient-temple-blue &,
  .card-gradient-lotus-pink &,
  .card-gradient-sunset &,
  .card-gradient-temple & {
    color: rgba(255, 255, 255, 0.9);
  }
}

// ===== CARD BODY =====
.card-body {
  flex: 1;
}

// ===== CARD FOOTER =====
.card-footer {
  border-top: 1px solid $gray-200;
  padding-top: $spacing-4;
  margin-top: $spacing-6;
  
  .card-glass & {
    border-top-color: rgba(255, 255, 255, 0.2);
  }
  
  .card-glass-dark & {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}

// ===== STAT CARDS =====
.stat-card {
  @include card-glass;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $gradient-temple;
  }
  
  &:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: $shadow-xl;
  }
}

.stat-card-khmer-gold {
  @extend .stat-card;
  
  &::before {
    background: $gradient-khmer-gold;
  }
  
  &:hover {
    box-shadow: $shadow-khmer-gold;
  }
}

.stat-card-temple-blue {
  @extend .stat-card;
  
  &::before {
    background: $gradient-temple-blue;
  }
  
  &:hover {
    box-shadow: $shadow-temple-blue;
  }
}

.stat-card-lotus-pink {
  @extend .stat-card;
  
  &::before {
    background: $gradient-lotus-pink;
  }
  
  &:hover {
    box-shadow: $shadow-lotus-pink;
  }
}

.stat-value {
  font-size: $font-size-3xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin-bottom: $spacing-2;
  @include text-shadow-soft;
  
  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

.stat-label {
  font-size: $font-size-sm;
  color: $gray-600;
  font-weight: $font-weight-medium;
  @include khmer-text;
}

// ===== ACTION CARDS =====
.action-card {
  @include card-glass;
  @include flex-column-center;
  cursor: pointer;
  transition: all $transition-base;
  min-height: 120px;
  
  &:hover {
    transform: translateY(-6px) scale(1.05);
    box-shadow: $shadow-xl;
    background: rgba(255, 255, 255, 0.4);
  }
  
  &:active {
    transform: translateY(-2px) scale(1.02);
  }
}

.action-icon {
  font-size: $font-size-4xl;
  margin-bottom: $spacing-3;
  @include floating-animation;
}

.action-title {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $gray-800;
  text-align: center;
  @include khmer-text;
}

// ===== ACTIVITY CARDS =====
.activity-card {
  @include card-glass;
  padding: $spacing-4;
  margin-bottom: $spacing-3;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:hover {
    transform: translateX(4px);
    box-shadow: $shadow-md;
  }
}

.activity-item {
  display: flex;
  align-items: center;
  padding: $spacing-3 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.activity-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: $primary;
  margin-right: $spacing-3;
  @include pulse-animation;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  color: $gray-800;
  margin-bottom: $spacing-1;
  @include khmer-text;
}

.activity-subtitle {
  font-size: $font-size-xs;
  color: $gray-600;
  @include khmer-text;
}

.activity-time {
  font-size: $font-size-xs;
  color: $gray-500;
  font-weight: $font-weight-medium;
}

// ===== RESPONSIVE CARDS =====
@include mobile-only {
  .card {
    padding: $spacing-4;
    margin-bottom: $spacing-3;
  }
  
  .card-header {
    padding-bottom: $spacing-3;
    margin-bottom: $spacing-4;
  }
  
  .card-footer {
    padding-top: $spacing-3;
    margin-top: $spacing-4;
  }
  
  .stat-value {
    font-size: $font-size-2xl;
  }
  
  .action-card {
    min-height: 100px;
  }
  
  .action-icon {
    font-size: $font-size-3xl;
    margin-bottom: $spacing-2;
  }
}
