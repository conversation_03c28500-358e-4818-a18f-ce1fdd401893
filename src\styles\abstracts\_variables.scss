// Khmer-Inspired Color Palette & Design Variables

// ===== KHMER GOLD PALETTE =====
$khmer-gold-50: #fffbeb;
$khmer-gold-100: #fef3c7;
$khmer-gold-200: #fde68a;
$khmer-gold-300: #fcd34d;
$khmer-gold-400: #fbbf24;
$khmer-gold-500: #f59e0b; // Primary Khmer Gold
$khmer-gold-600: #d97706;
$khmer-gold-700: #b45309;
$khmer-gold-800: #92400e;
$khmer-gold-900: #78350f;

// ===== TEMPLE BLUE PALETTE =====
$temple-blue-50: #f0f9ff;
$temple-blue-100: #e0f2fe;
$temple-blue-200: #bae6fd;
$temple-blue-300: #7dd3fc;
$temple-blue-400: #38bdf8;
$temple-blue-500: #0ea5e9; // Primary Temple Blue
$temple-blue-600: #0284c7;
$temple-blue-700: #0369a1;
$temple-blue-800: #075985;
$temple-blue-900: #0c4a6e;

// ===== LOTUS PINK PALETTE =====
$lotus-pink-50: #fdf2f8;
$lotus-pink-100: #fce7f3;
$lotus-pink-200: #fbcfe8;
$lotus-pink-300: #f9a8d4;
$lotus-pink-400: #f472b6;
$lotus-pink-500: #ec4899; // Primary Lotus Pink
$lotus-pink-600: #db2777;
$lotus-pink-700: #be185d;
$lotus-pink-800: #9d174d;
$lotus-pink-900: #831843;

// ===== SEMANTIC COLORS =====
$primary: $temple-blue-500;
$secondary: $khmer-gold-500;
$accent: $lotus-pink-500;
$success: #10b981;
$warning: $khmer-gold-500;
$danger: #ef4444;
$info: $temple-blue-400;

// ===== NEUTRAL COLORS =====
$white: #ffffff;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;
$black: #000000;

// ===== GRADIENTS =====
$gradient-khmer-gold: linear-gradient(135deg, #{$khmer-gold-400} 0%, #{$khmer-gold-600} 100%);
$gradient-temple-blue: linear-gradient(135deg, #{$temple-blue-400} 0%, #{$temple-blue-600} 100%);
$gradient-lotus-pink: linear-gradient(135deg, #{$lotus-pink-400} 0%, #{$lotus-pink-600} 100%);
$gradient-sunset: linear-gradient(135deg, #{$khmer-gold-400} 0%, #{$lotus-pink-500} 50%, #{$temple-blue-500} 100%);
$gradient-temple: linear-gradient(135deg, #{$temple-blue-500} 0%, #{$khmer-gold-500} 100%);

// ===== TYPOGRAPHY =====
$font-family-primary: 'Kantumruy Pro', 'Khmer OS', 'Khmer OS System', 'Noto Sans Khmer', sans-serif;
$font-family-secondary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;

$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;

$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// ===== SPACING =====
$spacing-0: 0;
$spacing-1: 0.25rem;   // 4px
$spacing-2: 0.5rem;    // 8px
$spacing-3: 0.75rem;   // 12px
$spacing-4: 1rem;      // 16px
$spacing-5: 1.25rem;   // 20px
$spacing-6: 1.5rem;    // 24px
$spacing-8: 2rem;      // 32px
$spacing-10: 2.5rem;   // 40px
$spacing-12: 3rem;     // 48px
$spacing-16: 4rem;     // 64px
$spacing-20: 5rem;     // 80px

// ===== BORDER RADIUS =====
$border-radius-none: 0;
$border-radius-sm: 0.25rem;   // 4px
$border-radius-base: 0.5rem;  // 8px
$border-radius-md: 0.75rem;   // 12px
$border-radius-lg: 1rem;      // 16px
$border-radius-xl: 1.5rem;    // 24px
$border-radius-full: 9999px;

// ===== SHADOWS =====
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// Colored shadows for glass morphism
$shadow-khmer-gold: 0 8px 32px rgba(245, 158, 11, 0.15);
$shadow-temple-blue: 0 8px 32px rgba(14, 165, 233, 0.15);
$shadow-lotus-pink: 0 8px 32px rgba(236, 72, 153, 0.15);

// ===== GLASS MORPHISM =====
$glass-backdrop-blur: blur(16px);
$glass-backdrop-blur-sm: blur(8px);
$glass-backdrop-blur-lg: blur(24px);

$glass-bg-light: rgba(255, 255, 255, 0.25);
$glass-bg-dark: rgba(0, 0, 0, 0.25);
$glass-border: rgba(255, 255, 255, 0.18);

// ===== BREAKPOINTS =====
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// ===== Z-INDEX =====
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// ===== TRANSITIONS =====
$transition-fast: 150ms ease-in-out;
$transition-base: 250ms ease-in-out;
$transition-slow: 350ms ease-in-out;

// ===== ANIMATION DURATIONS =====
$duration-fast: 0.15s;
$duration-base: 0.25s;
$duration-slow: 0.35s;
$duration-slower: 0.5s;
