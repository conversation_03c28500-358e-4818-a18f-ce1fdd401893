import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Employee } from '../types'

// ===== TYPES =====
interface AuthState {
  // State
  currentUser: Employee | null
  isLoading: boolean
  isAuthenticated: boolean
  
  // Actions
  login: (user: Employee) => void
  logout: () => void
  updateUser: (updates: Partial<Employee>) => void
  setLoading: (loading: boolean) => void
}

// ===== AUTH STORE =====
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentUser: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      login: (user: Employee) => {
        set({
          currentUser: user,
          isAuthenticated: true,
          isLoading: false
        })
      },

      logout: () => {
        set({
          currentUser: null,
          isAuthenticated: false,
          isLoading: false
        })
      },

      updateUser: (updates: Partial<Employee>) => {
        const { currentUser } = get()
        if (currentUser) {
          set({
            currentUser: { ...currentUser, ...updates }
          })
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      }
    }),
    {
      name: 'khmer-loan-auth', // localStorage key
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// ===== SELECTORS =====
export const useCurrentUser = () => useAuthStore((state) => state.currentUser)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.isLoading)
