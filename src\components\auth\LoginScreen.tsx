import { useState, useCallback } from '@lynx-js/react'
import { Employee } from '../../types/index'

interface LoginScreenProps {
  onLogin: (user: Employee) => void
}

// Mock users for demo (in real app, this would come from API)
const MOCK_USERS: Employee[] = [
  {
    id: '1',
    username: 'admin',
    fullName: 'Admin User',
    fullNameKhmer: 'អ្នកគ្រប់គ្រង',
    role: 'admin',
    phoneNumber: '+855123456789',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date(),
    lastLogin: new Date()
  },
  {
    id: '2',
    username: 'manager',
    fullName: 'Loan Manager',
    fullNameKhmer: 'អ្នកគ្រប់គ្រងកម្ចី',
    role: 'manager',
    phoneNumber: '+855987654321',
    isActive: true,
    createdAt: new Date()
  },
  {
    id: '3',
    username: 'officer',
    fullName: 'Loan Officer',
    fullNameKhmer: 'មន្ត្រីកម្ចី',
    role: 'officer',
    phoneNumber: '+855555666777',
    isActive: true,
    createdAt: new Date()
  }
]

export function LoginScreen({ onLogin }: LoginScreenProps) {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [language, setLanguage] = useState<'en' | 'km'>('en')

  const handleLogin = useCallback(async () => {
    if (!username.trim() || !password.trim()) {
      setError(language === 'km' ? 'សូមបញ្ចូលឈ្មោះអ្នកប្រើ និងពាក្យសម្ងាត់' : 'Please enter username and password')
      return
    }

    setIsLoading(true)
    setError('')

    // Simulate API call
    setTimeout(() => {
      const user = MOCK_USERS.find(u => u.username === username)

      if (user && password === 'password123') {
        const updatedUser = { ...user, lastLogin: new Date() }
        onLogin(updatedUser)
      } else {
        setError(language === 'km' ? 'ឈ្មោះអ្នកប្រើ ឬពាក្យសម្ងាត់មិនត្រឹមត្រូវ' : 'Invalid username or password')
      }

      setIsLoading(false)
    }, 1000)
  }, [username, password, onLogin, language])

  const toggleLanguage = useCallback(() => {
    setLanguage(prev => prev === 'en' ? 'km' : 'en')
  }, [])

  const texts = {
    en: {
      title: 'Loan Management System',
      subtitle: 'Employee Login',
      username: 'Username',
      password: 'Password',
      login: 'Login',
      demoInfo: 'Demo Users: admin, manager, officer (password: password123)',
      language: 'ភាសាខ្មែរ'
    },
    km: {
      title: 'ប្រព័ន្ធគ្រប់គ្រងកម្ចី',
      subtitle: 'ចូលប្រើប្រាស់សម្រាប់បុគ្គលិក',
      username: 'ឈ្មោះអ្នកប្រើ',
      password: 'ពាក្យសម្ងាត់',
      login: 'ចូល',
      demoInfo: 'អ្នកប្រើសាកល្បង: admin, manager, officer (ពាក្យសម្ងាត់: password123)',
      language: 'English'
    }
  }

  const t = texts[language]

  return (
    <view style={{
      flex: 1,
      backgroundColor: '#f8f9fa',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20
    }}>
      {/* Language Toggle */}
      <view style={{
        position: 'absolute',
        top: 40,
        right: 20
      }}>
        <text
          style={{
            color: '#007bff',
            fontSize: 16,
            textDecorationLine: 'underline'
          }}
          bindtap={toggleLanguage}
        >
          {t.language}
        </text>
      </view>

      {/* Logo and Title */}
      <view style={{
        alignItems: 'center',
        marginBottom: 40
      }}>
        <view style={{
          width: 80,
          height: 80,
          backgroundColor: '#007bff',
          borderRadius: 40,
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: 20
        }}>
          <text style={{
            color: 'white',
            fontSize: 32,
            fontWeight: 'bold'
          }}>
            ₹
          </text>
        </view>

        <text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: '#333',
          textAlign: 'center',
          marginBottom: 8
        }}>
          {t.title}
        </text>

        <text style={{
          fontSize: 16,
          color: '#666',
          textAlign: 'center'
        }}>
          {t.subtitle}
        </text>
      </view>

      {/* Login Form */}
      <view style={{
        width: '100%',
        maxWidth: 400,
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 30
      }}>
        {/* Username Input */}
        <view style={{ marginBottom: 20 }}>
          <text style={{
            fontSize: 14,
            fontWeight: '600',
            color: '#333',
            marginBottom: 8
          }}>
            {t.username}
          </text>
          <view
            style={{
              width: '100%',
              height: 48,
              borderWidth: 1,
              borderColor: '#ddd',
              borderRadius: 8,
              padding: 16,
              backgroundColor: '#fff',
              justifyContent: 'center'
            }}
            bindtap={() => {
              // In a real app, this would open a keyboard or input modal
              const newUsername = prompt('Enter username:') || username
              setUsername(newUsername)
            }}
          >
            <text style={{
              fontSize: 16,
              color: username ? '#333' : '#999'
            }}>
              {username || t.username}
            </text>
          </view>
        </view>

        {/* Password Input */}
        <view style={{ marginBottom: 20 }}>
          <text style={{
            fontSize: 14,
            fontWeight: '600',
            color: '#333',
            marginBottom: 8
          }}>
            {t.password}
          </text>
          <view
            style={{
              width: '100%',
              height: 48,
              borderWidth: 1,
              borderColor: '#ddd',
              borderRadius: 8,
              padding: 16,
              backgroundColor: '#fff',
              justifyContent: 'center'
            }}
            bindtap={() => {
              // In a real app, this would open a keyboard or input modal
              const newPassword = prompt('Enter password:') || password
              setPassword(newPassword)
            }}
          >
            <text style={{
              fontSize: 16,
              color: password ? '#333' : '#999'
            }}>
              {password ? '••••••••' : t.password}
            </text>
          </view>
        </view>

        {/* Error Message */}
        {error && (
          <text style={{
            color: '#dc3545',
            fontSize: 14,
            marginBottom: 20,
            textAlign: 'center'
          }}>
            {error}
          </text>
        )}

        {/* Login Button */}
        <view
          style={{
            backgroundColor: isLoading ? '#ccc' : '#007bff',
            borderRadius: 8,
            height: 48,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 20
          }}
          bindtap={isLoading ? undefined : handleLogin}
        >
          <text style={{
            color: 'white',
            fontSize: 16,
            fontWeight: '600'
          }}>
            {isLoading ? (language === 'km' ? 'កំពុងចូល...' : 'Logging in...') : t.login}
          </text>
        </view>

        {/* Demo Info */}
        <text style={{
          fontSize: 12,
          color: '#666',
          textAlign: 'center',
          lineHeight: 18
        }}>
          {t.demoInfo}
        </text>
      </view>
    </view>
  )
}
